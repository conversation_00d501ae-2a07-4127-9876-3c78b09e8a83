---
description: 
globs: 
alwaysApply: false
---
# Action/Controller类PowerMock单元测试规范

## 概述
本规范定义了需要使用PowerMock的单元测试实现方法，特别针对包含父类方法调用的测试场景。

## 适用范围
- 类名中包含Action或Controller的类的**特定方法**
- **具体方法中**使用了super关键字调用父类方法

## 基本规范
1. **命名规则**：PowerMock测试类必须命名为 `类名+PowerMockTest`
2. **测试类分离**：PowerMock测试必须单独创建测试类，不与普通单元测试混合
3. **必要注解**：必须添加以下PowerMock相关注解
   ```groovy
   @PowerMockRunnerDelegate(Sputnik)
   @RunWith(PowerMockRunner.class)
   @PrepareForTest([被测试类.class, 父类.class])
   @PowerMockIgnore(value =["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
   ```

## super调用场景分类与处理方式

### 场景1：方法中直接包含super.method()调用且有返回值
**处理方式**：
- **必须修改被测java类代码**，将super调用抽取为辅助方法，并在测试中mock该辅助方法
  ```java
  // 修改前
  public String method() {
      String result = super.method();
      return result + " extended";
  }

  // 修改后
  public String method() {
      String result = getSuperResult();
      return result + " extended";
  }

  private String getSuperResult() {
      return super.method();
  }
  ```

  ```groovy
  // 测试代码
  PowerMockito.doReturn("mocked result").when(spy, "getSuperResult");
  ```

### 场景2：方法中直接包含super.method()调用且无返回值
**处理方式**：
- **不需要修改被测Java类代码**，直接在测试中使用suppress禁止父类方法执行

  ```java
  // Java代码保持不变
  public void method() {
      super.method();
      // 其他业务逻辑
  }
  ```

  ```groovy
  // 测试代码
  // 禁止执行父类的method方法
  PowerMockito.suppress(PowerMockito.method(ParentClass.class, "method"));

  // 执行被测方法
  instance.method();

  // 进行必要的验证
  ```

### 场景3：方法调用另一个包含super的方法且有返回值
**处理方式**：直接mock被调用的辅助方法
```groovy
PowerMockito.doReturn("mocked result").when(spy, "helperMethod");
```

## 重要说明
- PowerMock测试仅适用于真正需要模拟父类方法调用的方法
- 同一个类中不需要模拟父类方法调用的方法应放在普通Spock测试类中，并使用 [t_paas-b-co-appframework-main.mdc](mdc:rules/t_paas-b-co-appframework-main.mdc) **普通Test类**规范
- 遵循最小化PowerMock使用范围的原则，仅在必要时使用

## 最佳实践
1. 对于有返回值的父类方法调用（场景1），**必须**使用私有辅助方法包装，便于测试
2. 对于无返回值的父类方法调用（场景2），**不要修改源代码**，直接在测试中使用PowerMockito.suppress
3. 在编写PowerMock测试前，确保理解被测试类的继承结构和方法调用链
4. 合理组织测试代码，每个测试方法专注于测试一个特定功能或分支