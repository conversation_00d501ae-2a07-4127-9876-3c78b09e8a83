package com.facishare.paas.appframework.core.exception;

/**
 * 参数校验异常
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/18.
 */
public class ValidateException extends AppBusinessException {

    public ValidateException(String message) {
        super(message, AppFrameworkErrorCode.VALIDATION_ERROR);
    }

    public ValidateException(String message, int errorCode) {
        super(message, errorCode);
    }

    public ValidateException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }
}
