package com.facishare.paas.appframework.core.exception;

/**
 * 框架层ErrorCode
 *
 * Created by liyi<PERSON><PERSON> on 2017/11/9.
 */
public enum AppFrameworkErrorCode implements ErrorCode {

    REQUEST_FORBIDDEN       (403),

    NO_PERMISSION           (320_001_400),
    VALIDATION_ERROR        (320_001_401),
    NO_DATA_PERMISSION      (320_001_402),
    REQUEST_RESOURCE_NOT_FOUND(320_001_404),

    ClASS_LOAD_ERROR        (320_001_500),
    INIT_HANDLER_ERROR      (320_001_501),
    PROCESSING_ALERT        (320_001_201),
    PREVIOUS_REQUEST_PROCESSING_ALERT        (320_001_202),

    META_DATA_ERROR         (320_002_500),
    OBJECT_DEF_NOT_FOUND_ERROR(320_002_404),
    LICENSE_ERROR           (320_003_500),
    APPROVAL_ERROR          (320_004_500),
    PAYMENT_ERROR           (320_005_500),
    RECORD_TYPE_ERROR       (320_006_500),
    SIGN_IN_OUT_ERROR       (320_007_500),
    UNLOCK_ERROR            (320_008_500),
    APPROVAL_FILTER_ERROR   (320_009_500),

    EXPRESSION_ERROR        (320_010_500),
    FIELD_NOT_EXIST_ERROR   (320_011_500),
    STAGE_THRUSTER_ERROR    (320_012_500),
    FUNCTION_ERROR          (320_013_500),
    FUNCTION_USER_BIZ_ERROR (320_013_501),
    DUPLICATED_DATA_ERROR   (320_014_500),

    DATA_NOT_FOUND_ERROR    (201112008),
    EXPORT_ERROR            (201112009),

    UNIQUE_RULE_VALIDATION_ERROR(40_000_420),

    NOT_ELEMENT_PRESENT_ERROR(201_001_404);


    AppFrameworkErrorCode(int code) {
        this.code = code;
    }

    int code;

    public int getCode() {
        return code;
    }

}
