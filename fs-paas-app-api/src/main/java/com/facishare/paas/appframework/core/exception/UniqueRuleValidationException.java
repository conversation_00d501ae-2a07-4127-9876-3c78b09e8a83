package com.facishare.paas.appframework.core.exception;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/09/09
 */
public class UniqueRuleValidationException extends AppBusinessException {
    public UniqueRuleValidationException(String message) {
        super(message, AppFrameworkErrorCode.UNIQUE_RULE_VALIDATION_ERROR);
    }

    public UniqueRuleValidationException(String message, int errorCode) {
        super(message, errorCode);
    }

    public UniqueRuleValidationException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }

}
