package com.facishare.paas.appframework.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for service module
 * <p>
 * Created by liyiguang on 2017/7/30.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ServiceModule {
    String value();

    String description() default "";

    /**
     * 所属模块名称
     * @return
     */
    String name() default "";
}
