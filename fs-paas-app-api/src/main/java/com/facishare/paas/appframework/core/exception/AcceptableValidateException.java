package com.facishare.paas.appframework.core.exception;

/**
 * Created by zhouwr on 2019/1/11
 */
public class AcceptableValidateException extends AppBusinessException {
    private Object validateResult;

    public AcceptableValidateException(Object validateResult) {
        super("It's ok", 200);
        this.validateResult = validateResult;
    }

    public Object getValidateResult() {
        return validateResult;
    }
}
