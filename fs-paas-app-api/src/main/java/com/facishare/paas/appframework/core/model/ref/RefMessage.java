package com.facishare.paas.appframework.core.model.ref;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface RefMessage {

    @Getter
    enum ActionType {

        CREATE("create"), DELETE("delete"), DELETE_AND_CREATE("delete_and_create");

        final private String code;
        ActionType(String code) {
            this.code = code;
        }

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Refs {
        @JsonProperty("refs")
        @JSONField(name = "refs")
        @SerializedName(value = "refs")
        private List<Ref> refs;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Ref {
        /**
         * 租户ID
         */
        @JsonProperty("tenant_id")
        @JSONField(name = "tenant_id")
        @SerializedName(value = "tenant_id")
        private String tenantId;
        /**
         * 关系操作动作：create/delete/delete_and_create
         */
        @JsonProperty("action")
        @JSONField(name = "action")
        @SerializedName(value = "action")
        private String action;

        /**
         * 增加关系参数
         */
        @JsonProperty("create_args")
        @JSONField(name = "create_args")
        @SerializedName(value = "create_args")
        private List<CreateArg> createArgs;
        /**
         * 删除关系参数
         */
        @JsonProperty("delete_args")
        @JSONField(name = "delete_args")
        @SerializedName(value = "delete_args")
        private List<DeleteArg> deleteArgs;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class CreateArg {

        /**
         * 应用方（source）的类型，取自配置文件 find_where_an_entity_is_used.refType
         */
        @JsonProperty("ref_type")
        @JSONField(name = "ref_type")
        @SerializedName(value = "ref_type")
        private String refType;

        /**
         * 应用方（source）的表现类型，取自配置文件 find_where_an_entity_is_used.entity[keyName]
         * refType#表现类型 作为 source_value 值存储
         */
        @JsonProperty("source_display_type")
        @JSONField(name = "source_display_type")
        @SerializedName(value = "source_display_type")
        private String sourceDisplayType;
        /**
         * 应用方（source）的默认多语表达，需要多个使用 #%$ 拼接
         */
        @JsonProperty("source_label")
        @JSONField(name = "source_label")
        @SerializedName(value = "source_label")
        private String sourceLabel;
        /**
         * 应用方（source）的唯一标识，一般使用 API，需要多个使用 . 拼接
         */
        @JsonProperty("source_value")
        @JSONField(name = "source_value")
        @SerializedName(value = "source_value")
        private String sourceValue;
        /**
         * 供应者（target）的类型，元数据提供的 com.facishare.paas.reference.service.EntityReferenceService 只能选择其中类型，如果不在其中，则不会执行入库
         */
        @JsonProperty("target_type")
        @JSONField(name = "target_type")
        @SerializedName(value = "target_type")
        private String targetType;
        /**
         * 供应者（target）的唯一标识，一般使用 API，需要多个使用 . 拼接，与 targetType 对应匹配
         */
        @JsonProperty("target_value")
        @JSONField(name = "target_value")
        @SerializedName(value = "target_value")
        private String targetValue;
        /**
         * 供应者（target）的默认多语表达，需要多个使用 #%$ 拼接
         */
        @JsonProperty("target_label")
        @JSONField(name = "target_label")
        @SerializedName(value = "target_label")
        private String targetLabel;



    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class DeleteArg {
        /**
         * 应用方（source）的类型，取自配置文件 find_where_an_entity_is_used.refType
         */
        @JsonProperty("ref_type")
        @JSONField(name = "ref_type")
        @SerializedName(value = "ref_type")
        private String refType;
        /**
         * 已存档的应用方（source）的表现类型，取自配置文件 find_where_an_entity_is_used.entity[keyName]
         * refType#表现类型 作为 source_value 值进行删除条件
         */
        @JsonProperty("archived_types")
        @JSONField(name = "archived_types")
        @SerializedName(value = "archived_types")
        private Set<String> archivedSourceDisplayTypes;
        /**
         * 匹配应用方（source）的形式，默认为EQ，可选 EQ、START_WITH
         */
        @JsonProperty("source_match_type")
        @JSONField(name = "source_match_type")
        @SerializedName(value = "source_match_type")
        private String sourceMatchType;
        /**
         * 应用方（source）的唯一标识，一般使用 API，需要多个使用 . 拼接
         */
        @JsonProperty("source_value")
        @JSONField(name = "source_value")
        @SerializedName(value = "source_value")
        private String sourceValue;
        /**
         * 供应者（target）匹配类型，默认为EQ，可选 EQ、START_WITH
         */
        @JsonProperty("target_match_type")
        @JSONField(name = "target_match_type")
        @SerializedName(value = "target_match_type")
        private String targetMatchType;
        /**
         * 供应者（target）的唯一标识，一般使用 API，需要多个使用 . 拼接，与 targetType 对应匹配
         */
        @JsonProperty("target_type")
        @JSONField(name = "target_type")
        @SerializedName(value = "target_type")
        private String targetType;
        /**
         * 供应者（target）的默认多语表达，需要多个使用 #%$ 拼接
         */
        @JsonProperty("target_value")
        @JSONField(name = "target_value")
        @SerializedName(value = "target_value")
        private String targetValue;
    }
}
