package com.facishare.paas.appframework.core.exception;

public class NotElementPresentException extends AppBusinessException {

    public NotElementPresentException(String message) {
        super(message, AppFrameworkErrorCode.NOT_ELEMENT_PRESENT_ERROR);
    }

    public NotElementPresentException(String message, Throwable cause) {
        super(message, cause, AppFrameworkErrorCode.NOT_ELEMENT_PRESENT_ERROR);
    }

    public NotElementPresentException(String message, int errorCode) {
        super(message, errorCode);
    }
}
