package com.facishare.paas.appframework.core.model;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * Created by liyiguang on 2017/8/23.
 */
public class RequestContextManager {

    private static final ThreadLocal<RequestContext> THREAD_LOCAL = new ThreadLocal<>();
    private static final List<ContextListener> contextSetListeners = Lists.newArrayList();
    private static final List<ContextListener> contextRemoveListeners = Lists.newArrayList();

    public static RequestContext getContext() {
        return THREAD_LOCAL.get();
    }

    public static void setContext(RequestContext requestContext) {
        if (requestContext == null) {
            return;
        }
        THREAD_LOCAL.set(requestContext);
        I18N.setContext(requestContext.getTenantId(), requestContext.getLang().getValue());
        //绑定国际化资源
//        MessageResourceManager.setMessageResource(new MessageResource(requestContext.getLang().getValue()));

        contextSetListeners.forEach(x -> x.apply(requestContext));
    }

    public static void removeContext() {
        RequestContext context = THREAD_LOCAL.get();
        THREAD_LOCAL.remove();
        I18N.clearContext();
        TimeZoneContextHolder.clearContext();
        MultiRegionContextHolder.clearContext();
        Lang.clear();
//        MessageResourceManager.removeResource();
        contextRemoveListeners.forEach(x -> x.apply(context));
    }

    public static void addContextAddListener(ContextListener listener) {
        if (listener != null) {
            contextSetListeners.add(listener);
        }
    }

    public static void addContextRemoveListener(ContextListener listener) {
        if (listener != null) {
            contextRemoveListeners.add(listener);
        }
    }

    public static <T> T runWithContext(RequestContext context, Supplier<T> supplier) {
        RequestContext oldContext = RequestContextManager.getContext();
        TraceContext traceContext = TraceContext.get();
        String locale = traceContext.getLocale();
        RequestContextManager.setContext(context);
        Optional.ofNullable(context)
                .map(RequestContext::getLang)
                .map(Lang::getValue)
                .ifPresent(traceContext::setLocale);
        try {
            return supplier.get();
        } finally {
            RequestContextManager.setContext(oldContext);
            traceContext.setLocale(locale);
        }
    }

    public interface ContextListener {
        /**
         * @param context xxx
         */
        void apply(RequestContext context);
    }
}
