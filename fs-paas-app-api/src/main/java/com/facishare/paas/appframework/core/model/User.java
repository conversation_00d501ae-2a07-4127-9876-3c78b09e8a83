package com.facishare.paas.appframework.core.model;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;

/**
 * 用户身份
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@Getter
@Slf4j
public class User {

    public static final String SUPPER_ADMIN_USER_ID = "-10000";
    public static final int SUPPER_ADMIN_USER_ID_INT = -10000;
    public static final String COMPANY_ID = "999999";

    //租户
    private final String tenantId;
    //内部用户
    private String userId;
    //内部用户名
    private String userName;

    //外部用户Id
    private String outUserId;
    //外部企业
    private String outTenantId;
    //上游负责人（可能是互联企业的合作伙伴负责人、或者互联企业的客户负责人）
    @Setter
    private String upstreamOwnerId;

    public User(String tenantId, String userId) {
        this.tenantId = tenantId;
        this.userId = userId;
    }

    public User(String tenantId, String userId, String outUserId, String outTenantId) {
        this(tenantId, userId, outUserId, outTenantId, null);
    }

    @Builder
    @JsonCreator
    public User(@JsonProperty("tenantId") String tenantId, @JsonProperty("userId") String userId,
                @JsonProperty("outUserId") String outUserId, @JsonProperty("outTenantId") String outTenantId,
                @JsonProperty("upstreamOwnerId") String upstreamOwnerId) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.outUserId = outUserId;
        this.outTenantId = outTenantId;
        if (!Strings.isNullOrEmpty(outUserId) && isGrayTenant()) {
            this.userId = null;
        }
        this.upstreamOwnerId = upstreamOwnerId;
    }

    @JsonIgnore
    public boolean isGrayTenant() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_NULL_GRAY, tenantId);
    }

    @JsonIgnore
    @Setter
    private Optional<Boolean> isCrmAdmin = Optional.empty();

    public static User systemUser(String tenantId) {
        return new User(tenantId, SUPPER_ADMIN_USER_ID);
    }

    public static String getSupperAdminUserName() {
        return I18N.text(I18NKey.SYSTEM);
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 检查是否为超级用户
     *
     * @return
     */
    @JsonIgnore
    public boolean isSupperAdmin() {
        return SUPPER_ADMIN_USER_ID.equals(userId);
    }

    @JsonIgnore
    public String getUserName() {
        if (isSupperAdmin()) {
            return getSupperAdminUserName();
        } else {
            return userName;
        }
    }

    @JsonIgnore
    public boolean isOutUser() {
        return (!Strings.isNullOrEmpty(outTenantId) && !Strings.isNullOrEmpty(outUserId));
    }

    @JsonIgnore
    public String getUserIdOrOutUserIdIfOutUser() {
        if (isOutUser()) {
            return outUserId;
        }
        return userId;
    }

    public void validate() {
        if (Strings.isNullOrEmpty(getTenantId())) {
            throw new PermissionError(I18N.text(I18NKey.NO_EXIST_TENANT));
        }
    }

    @JsonIgnore
    public Integer getUserIdInt() {
        return Strings.isNullOrEmpty(userId) ? null : Integer.valueOf(userId);
    }

    @JsonIgnore
    public Integer getTenantIdInt() {
        return Strings.isNullOrEmpty(tenantId) ? null : Integer.valueOf(tenantId);
    }

    @JsonIgnore
    public Long getOutTenantIdLong() {
        return Strings.isNullOrEmpty(outTenantId) ? null : Long.valueOf(outTenantId);
    }

    @JsonIgnore
    public Long getOutUserIdLong() {
        return Strings.isNullOrEmpty(outUserId) ? null : Long.valueOf(outUserId);
    }

    @JsonIgnore
    public String getUpstreamOwnerIdOrUserId() {
        if (isGrayTenant()) {
            return isOutUser() ? upstreamOwnerId : userId;
        }
        return isOutUser() ? (Strings.isNullOrEmpty(upstreamOwnerId) ? userId : upstreamOwnerId) : userId;
    }

    // 外部身份时userid的逻辑，给流程灰度时使用，全网后可以删除
    @JsonIgnore
    public String getUserIdWithFlowGray() {
        return isGrayTenant() ? getUserId() : getUpstreamOwnerIdOrUserId();
    }

    //是否外部游客用户
    @JsonIgnore
    public boolean isOutGuestUser() {
        return this.isOutUser() && Objects.equals(getOutUserIdLong(), 1000000000L)
                && Objects.equals(getOutTenantIdLong(), 1000000000L);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("tenantId", tenantId)
                .add("userId", userId)
                .add("outUserId", outUserId)
                .add("outTenantId", outTenantId)
                .add("upstreamOwnerId", upstreamOwnerId)
                .toString();
    }
}