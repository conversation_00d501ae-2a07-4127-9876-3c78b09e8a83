package com.facishare.paas.appframework.core.exception;

public enum SystemErrorCode {

    ACTION_LOAD_ERROR("s301990001", "Action服务加载异常。"),
    ACTION_FOUND_ERROR("s301990002", "Action定义没有找到。"),
    ACTION_INSTANTIATION_ERROR("s301990003", "Action实例化错误。"),
    CONFIG_CREATE_ERROR("s301990004", "创建租户配置错误。"),
    CONFIG_UPDATE_ERROR("s301990005", "更新租户配置错误。"),
    CONFIG_DELETE_ERROR("s301990006", "删除租户配置错误。"),
    CONFIG_QUERY_ERROR("s301990007", "查询租户配置错误。"),
    CONFIG_BATCH_QUERY_ERROR("s301990008", "批量查询租户配置错误。"),
    CONTROLLER_LOAD_ERROR("s301990009", "Controller服务加载异常。"),
    CONTROLLER_FOUND_ERROR("s301990010", "Controller服务没有找到。"),
    CONTROLLER_INSTANTIATION_ERROR("s301990011", "Controller实例化错误。"),
    METADATA_ERROR("s301990012", "系统异常。"),
    METADATA_TOP_INFO_ERROR("s301990013", "获取顶部信息错误。"),
    METADATA_COMPONENTS_ERROR("s301990014", "获取组建错误。"),
    METADATA_UPLOAD_ERROR("s301990015", "上传文件错误。"),
    METADATA_TIMEOUT_ERROR("s301990016", "系统超时。"),
    METADATA_OWNER_ID_ERROR("s301990017", "负责人ID错误。"),
    METADATA_SIGN_INFO_ERROR("s301990018", "获取注册信息异常。"),
    MQ_INIT_ERROR("s301990019", "服务初始化异常。"),
    MQ_SHUT_DOWN_ERROR("s301990020", "服务已关闭。"),
    MQ_ERROR("s301990021", "消息服务异常。"),
    INVOKE_ERROR("s301990022", "调用服务异常。"),
    SERVICE_NOT_FOUND_ERROR("s301990023", "没有找到相关服务"),
    JSON_SERIALIZE_ERROR("s301990024", "序列化错误"),
    INVALID_EXTENSION("s312070021", "扩展名不合法"),
    FILE_NOT_EXIST_OR_EXPIRED("s312010004", "文件不存在或已过期");

    ;

    String code;
    String description;

    SystemErrorCode(String code, String description){
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
