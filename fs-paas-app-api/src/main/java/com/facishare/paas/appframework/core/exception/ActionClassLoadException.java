package com.facishare.paas.appframework.core.exception;

/**
 * Action class 加载异常
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/21.
 */
public class ActionClassLoadException extends APPException {
    public ActionClassLoadException(String message) {
        super(message);
    }

    public ActionClassLoadException(String message, Throwable cause) {
        super(message, cause);
    }

    public ActionClassLoadException(SystemErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
    public ActionClassLoadException(SystemErrorCode errorCode) {
        super(errorCode.getCode());
    }
}
