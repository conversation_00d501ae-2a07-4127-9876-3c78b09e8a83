package com.facishare.paas.appframework.core.exception;

/**
 * 业务异常
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/23.
 */
public abstract class AppBusinessException extends RuntimeException {

    protected ErrorCode errorCode;

    protected int status = 200; //default 200

    public AppBusinessException(String message, int errorCode) {
        super(message);
        this.errorCode = () -> errorCode;
    }

    public AppBusinessException(String message, ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public AppBusinessException(String message, Throwable cause, ErrorCode errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        if (errorCode == null) {
            return 0;
        }
        return errorCode.getCode();
    }


    public boolean isSupportI18nCode() {
        return false;
    }

    public int getStatus() {
        return status;
    }
}
