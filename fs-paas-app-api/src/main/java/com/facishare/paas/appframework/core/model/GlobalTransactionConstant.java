package com.facishare.paas.appframework.core.model;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/9
 */
public interface GlobalTransactionConstant {
    /**
     * act handler 执行完成
     */
    String DO_ACT_COMPLETE = "do_act_complete";
    /**
     * 同步触发审批流成功
     */
    String APPROVAL_TRIGGER_STATUS = "approval_trigger_status";
    /**
     * 同步触发审批流成功，或异步触发了审批流
     */
    String APPROVAL_FLOW_START_SUCCESS_OR_ASYNCHRONOUS = "approval_flow_start_success_or_asynchronous";
    /**
     * 主数据生命状态
     */
    String OBJECT_SAVE_LIFE_STATUS = "object_save_life_status";
    /**
     * 写数据成功标记
     */
    String OBJECT_SAVE_WRITE_DB_FLAG = "object_save_write_db_flag";
    /**
     * 作废成功的数据 ids
     */
    String OBJECT_INVALID_SUCCESS_IDS = "object_invalid_success_ids";
}
