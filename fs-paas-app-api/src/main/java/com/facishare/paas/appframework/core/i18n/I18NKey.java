package com.facishare.paas.appframework.core.i18n;

public interface I18NKey {

    String LOCK_STATUS_UNLOCK = "I18nCRMSystemObj.field.lock_status.option.0";
    String LOCK_STATUS_LOCKED = "I18nCRMSystemObj.field.lock_status.option.1";
    String LIFE_STATUS_INEFFECTIVE = "I18nCRMSystemObj.field.life_status.option.ineffective";
    String LIFE_STATUS_UNDER_REVIEW = "I18nCRMSystemObj.field.life_status.option.under_review";
    String LIFE_STATUS_NORMAL = "I18nCRMSystemObj.field.life_status.option.normal";
    String LIFE_STATUS_IN_CHANGE = "I18nCRMSystemObj.field.life_status.option.in_change";
    String LIFE_STATUS_INVALID = "I18nCRMSystemObj.field.life_status.option.invalid";

    String UNSUPPORT_OPERATION = "paas.udobj.unsupport_operation";
    String UNSUPPORT_OPERATION_REVERSE = "paas.udobj.unsupport_operation_reverse";
    String NO_EXIST_TENANT = "paas.udobj.no_exist_tenant";
    String CODE_ABNORMAL = "paas.udobj.code_abnormal";

    String custom_action = "paas.udobj.custom_button";
    String stage_change = "paas.flow.approval.stage_change";
    String apprflow_trigger_abnormal = "paas.udobj.apprflow.trigger_abnormal";
    String apprflow_trigger_success = "paas.udobj.apprflow.trigger_success";
    String apprflow_exist_processing = "paas.udobj.apprflow.exist_processing";
    String apprflow_trigger_fail = "paas.udobj.apprflow.trigger_fail";
    String apprflow_no_exist = "paas.udobj.apprflow.no_exist";
    String apprflow_filter_abnormal = "paas.udobj.apprflow.filter_abnormal";
    String apprflow_restart = "paas.udobj.apprflow.restart";
    String apprflow_dept_higher = "paas.udobj.apprflow.dept_higher";
    String ACTION_BULK_HANG_TAG = "paas.udobj.action.bulk.hang.tag";
    String action_add = "paas.udobj.action.add";
    String action_edit = "paas.udobj.action.edit";
    String action_view_all = "paas.udobj.action.view_all";
    String action_edit_all = "paas.udobj.action.edit_all";
    String action_batch_edit = "paas.udobj.action.batch_edit";
    String action_save = "paas.udobj.action.save";
    String action_submit = "paas.udobj.action.submit";
    String action_add_save = "paas.udobj.action.add.save";
    String action_edit_save = "paas.udobj.action.edit.save";
    String action_add_save_continue = "paas.udobj.action.add.save.continue";
    String action_add_submit_continue = "paas.udobj.action.add.submit.continue";
    String action_add_save_draft = "paas.udobj.action.add.save.draft";
    String draft_data_deleted = "paas.udobj.draft.data.deleted";
    String action_add_save_add_contact = "paas.udobj.action.add.save.add_contact";
    String action_add_save_add_obj = "paas.udobj.action.add.save.add_obj";
    String action_add_submit_add_obj = "paas.udobj.action.add.submit.add_obj";
    String action_cancel = "paas.udobj.action.cancel";
    String action_single_add = "paas.udobj.action.single.add";
    String action_import_excel = "paas.udobj.action.import_excel";
    String action_batch_lookup_add = "paas.udobj.action.batch.lookup.add";
    String ACTION_TILE = "paas.udobj.action.tile";
    String ACTION_INSERT = "paas.udobj.action.insert";
    String action_invalid = "paas.udobj.constant.status_invalid";
    String action_delete = "paas.udobj.action.delete";
    String action_view_detail = "paas.udobj.action.view_detail";
    String action_view_list = "paas.udobj.action.view_list";
    String action_import = "paas.udobj.action.import";
    String action_export = "paas.udobj.action.export";
    String action_recover = "paas.udobj.action.recover";
    String action_print = "paas.udobj.action.print";
    String action_change_owner = "paas.udobj.action.change_owner";
    String action_add_team_member = "paas.udobj.action.add_team_member";
    String action_edit_team_member = "paas.udobj.action.edit_team_member";
    String action_delete_team_member = "paas.udobj.action.delete_team_member";
    String action_relate = "paas.udobj.action.relate";
    String action_bulk_relate = "paas.udobj.action.relate";
    String action_bulk_disrelate = "paas.udobj.action.bulk_disrelate";
    String action_bulk_delete = "paas.udobj.action.bulk_delete";
    String aaction_bulk_invalid = "paas.udobj.action.bulk_invalid";
    String action_bulk_recover = "paas.udobj.action.bulk_recover";
    String action_start_bpm = "paas.udobj.action.start_bpm";
    String action_view_whole_bpm = "paas.udobj.action.view_whole_bpm";
    String action_stop_bpm = "paas.udobj.action.stop_bpm";
    String action_change_bpm_approver = "paas.udobj.action.change_bpm_approver";
    String action_add_event = "paas.udobj.action.add_event";
    String action_sign_in = "paas.udobj.action.sign_in";
    String action_sign_out = "paas.udobj.action.sign_out";
    String action_pay = "paas.udobj.action.pay";
    String action_intelligent_form = "paas.udobj.action.intelligent_form";
    String action_lock = "paas.udobj.action.lock";
    String action_unlock = "paas.udobj.action.unlock";
    String action_modifylog_recover = "paas.udobj.action.modifylog_recover";
    String action_sale_record = "paas.udobj.action.sale_record";
    String action_service_record = "paas.udobj.action.service_record";
    String action_dial = "paas.udobj.action.dial";
    String action_send_mail = "paas.udobj.action.send_mail";
    String action_discuss = "paas.udobj.action.discuss";
    String action_schedule = "paas.udobj.action.schedule";
    String action_remind = "paas.udobj.action.remind";
    String action_clone = "paas.udobj.copy";
    String action_view_feed_card = "paas.udobj.action.view_feed_card";
    String action_change_partner = "paas.udobj.change_partner";
    String action_change_partner_owner = "paas.udobj.change_partner_owner";
    String action_delete_partner = "paas.udobj.delete_partner";
    String start_stage_propellor = "paas.udobj.start_stage_propellor";
    String action_unknow_action = "paas.udobj.action.unknow_action";
    String action_add_spec = "paas.udobj.action.add_spec";
    String action_return = "paas.udobj.action.return";
    String action_move = "paas.udobj.action.move";
    String action_choose = "paas.udobj.action.choose";
    String action_allocate = "paas.udobj.action.allocate";
    String action_take_back = "paas.udobj.action.take_back";
    String action_collect = "paas.udobj.action.collect";
    String action_update_deal_status = "paas.udobj.action.update_deal_status";
    String action_save_to_phone = "paas.udobj.action.save_to_phone";
    String action_change_sale_action = "paas.udobj.action.change_sale_action";
    String action_collect_to = "paas.udobj.action.collect_to";
    String action_transfer = "paas.udobj.action.transfer";
    String action_transfer_ui = "paas.udobj.action.transfer_ui";
    String action_recalculate = "paas.udobj.action.recalculate";
    String action_deal = "paas.udobj.action.deal";
    String action_close = "paas.udobj.action.close";
    String action_follow_up = "paas.udobj.action.follow_up";
    String action_recall = "paas.udobj.action.recall";
    String action_view_logistics = "paas.udobj.action.view_logistics";
    String action_confirm_receipt = "paas.udobj.action.confirm_receipt";
    String action_confirm_delivery = "paas.udobj.action.confirm_delivery";
    String action_add_delivery_not = "paas.udobj.action.add_delivery_not";
    String action_confirm_inbound = "paas.udobj.action.confirm_inbound";
    String action_status_off = "paas.udobj.action.status_off";
    String action_status_on = "paas.udobj.action.status_on";
    String action_view_before_sale_action = "paas.udobj.action.view_before_sale_action";
    String action_view_after_sale_action = "paas.udobj.action.view_after_sale_action";
    String action_confirm = "paas.udobj.action.confirm";
    String action_reject = "paas.udobj.action.reject";
    String action_change_confirmor = "paas.udobj.action.change_confirmor";
    String action_attach_upload_delete = "paas.udobj.action.attach_upload_delete";
    String action_view_attach = "paas.udobj.action.view_attach";
    String action_udobj_combine = "paas.udobj.combine";
    String action_udobj_combine_ui = "paas.udobj.combine.ui";
    String action_before_sale_action = "paas.udobj.action.before_sale_action";
    String action_after_sale_action = "paas.udobj.action.after_sale_action";
    String action_upload = "paas.udobj.action.upload";
    String action_cost_list = "paas.udobj.action.cost_list";
    String action_mark_mql = "paas.udobj.action.mark_mql";
    String action_extend_expireTime = "paas.flow.approval.extendExpireTime";
    String action_edit_atlas = "paas.udobj.action.edit_atlas";
    String action_priority = "paas.udobj.action.priority";
    String action_AddCampaignMembers = "sfa.udobj.action.saveasobject";
    String ACTION_PICTURE_ANNEX_DOWNLOAD = "paas.udobj.action_picture_annex_download";

    //新加的4个key
    String action_audit_customer = "paas.udobj.action.confirm";
    String action_change_auditor = "paas.udobj.change_auditor";
    String action_add_attach = "sfa.BasicSettingBusiness.468.1";
    String action_change_stage = "paas.udobj.change_stage";
    String action_scan_card = "paas.udobj.action.scan_card";
    String action_import_from_address_book = "paas.udobj.action.import_from_address_book";
    String complete_settlement = "paas.udobj.action.complete_settlement";

    String action_view_approval_instance_log = "paas.udobj.action.view_approval_instance_log";
    String action_view_approval_config = "paas.udobj.action.view_approval_config";

    String unsupport_date_type = "paas.udobj.unsupport_date_type";
    String datetime_convert_abnormal = "paas.udobj.datetime_convert_abnormal";
    String date_convert_abnormal = "paas.udobj.date_convert_abnormal";
    String action_choose_param_method = "paas.udobj.choose_param_method";
    String send_remind = "paas.udobj.send_remind";

    String constant_owner = "paas.udobj.constant.owner";
    String constant_dept = "paas.udobj.constant.dept";
    String constant_owner_dept = "paas.udobj.constant.owner_dept";
    String constant_relevant_team = "paas.udobj.constant.relevant_team";
    String constant_team_member_role = "paas.udobj.constant.team_member_role";
    String constant_team_member = "paas.udobj.constant.team_member";
    String constant_team_member_permission = "paas.udobj.constant.team_member_permission";
    String constant_lock_rule = "paas.udobj.constant.lock_rule";
    String constant_lock_status = "paas.udobj.constant.lock_status";
    String constant_lock_user = "paas.udobj.constant.lock_user";
    String constant_life_status = "paas.udobj.constant.life_status";
    String constant_before_invalid_status = "paas.udobj.constant.before_invalid_status";
    String constant_status_ineffective = "paas.udobj.constant.status_ineffective";
    String constant_status_under_review = "paas.udobj.constant.status_under_review";
    String constant_status_normal = "paas.udobj.constant.status_normal";
    String constant_status_in_change = "paas.udobj.constant.status_in_change";
    String constant_status_invalid = "paas.udobj.constant.status_invalid";

    String one_to_one = "paas.udobj.one_to_one";
    String one_to_many = "paas.udobj.one_to_many";
    String many_to_one = "paas.udobj.many_to_one";
    String many_to_many = "paas.udobj.many_to_many";
    String CONSTANT_NO_PERMISSION = "paas.udobj.no_permission";
    String constant_read_only = "paas.udobj.constant.read_only";
    String constant_read_write = "paas.udobj.read_write";

    String constant_private = "paas.udobj.constant.private";
    String constant_public_read = "paas.udobj.constant.public_read";
    String constant_public_read_write = "paas.udobj.constant.public_read_write";
    String constant_not_exist = "paas.udobj.constant.not_exist";
    String constant_normal_staff = "paas.udobj.constant.normal_staff";
    String constant_follower = "paas.udobj.constant.follower";
    String constant_service_staff = "paas.udobj.constant.service_staff";
    String OUTER_NORMAL_STAFF = "paas.udobj.outer.normal_staff";

//    public final static String a = "AccountObj.attribute.self.display_name";
//    public final static String a = "ProductObj.attribute.self.display_name";
//    public final static String a = "SalesOrderObj.attribute.self.display_name";
//    public final static String a = "ContactObj.attribute.self.display_name";
//    public final static String a = "LeadsObj.attribute.self.display_name";
//    public final static String a = "AccountObj.attribute.self.display_name";
//    public final static String a = "RefundObj.attribute.self.display_name";
//    public final static String a = "ContractObj.attribute.self.display_name";
//    public final static String a = "OpportunityObj.attribute.self.display_name";

    String ERRORCODE_SUCCESS = "paas.udobj.errorcode.success";
    String ERRORCODE_FAIL = "paas.udobj.errorcode.fail";
    String ERRORCODE_PAAS_PERMISSION_ERROR = "paas.udobj.errorcode.paas_permission_error";
    String ERRORCODE_MINI_VERSION = "paas.udobj.errorcode.mini_version";
    String ERRORCODE_VERSION_ERROR = "paas.udobj.errorcode.version_error";
    String ERRORCODE_MAX_ROLE = "paas.udobj.errorcode.max_role";
    String ERRORCODE_BASIC_VERSION = "paas.udobj.errorcode.basic_version";
    String ERRORCODE_UNKNOW_ERROR = "paas.udobj.errorcode.unknow_error";
    String ERRORCODE_CONFIG_FILE_FAIL = "paas.udobj.errorcode.config_file_fail";
    String ERRORCODE_PARAM_WRONG = "paas.udobj.errorcode.param_wrong";
    String ERRORCODE_USER_ID_EMPTY = "paas.udobj.errorcode.user_id_empty";
    String ERRORCODE_EI_EMPTY = "paas.udobj.errorcode.ei_empty";
    String ERRORCODE_EA_EMPTY = "paas.udobj.errorcode.ea_empty";
    String ERRORCODE_FAIL_CONVERT_APINAME = "paas.udobj.errorcode.fail_convert_apiname";
    String ERRORCODE_REST_CALL_FAIL = "paas.udobj.errorcode.rest_call_fail";
    String ERRORCODE_ON_SALE_FAIL = "paas.udobj.errorcode.on_sale_fail";
    String ERRORCODE_CHANGE_OWNER_FAIL = "paas.udobj.errorcode.change_owner_fail";
    String ERRORCODE_FIELD_UPDATE_FAIL = "paas.udobj.errorcode.field_update_fail";
    String ERRORCODE_DATA_CREATE_FAIL = "paas.udobj.errorcode.data_create_fail";
    String ERRORCODE_REST_RESULT_EMPTY = "paas.udobj.errorcode.rest_result_empty";
    String ERRORCODE_REST_RESULT_ERROR = "paas.udobj.errorcode.rest_result_error";
    String ERRORCODE_HTTP_204 = "paas.udobj.errorcode.http_204";
    String ERRORCODE_HTTP_400 = "paas.udobj.errorcode.http_400";
    String ERRORCODE_HTTP_404 = "paas.udobj.errorcode.http_404";
    String ERRORCODE_NO_REFERENCE = "paas.udobj.errorcode.no_reference";
    String ERRORCODE_LAYOUT_NOT_INIT = "paas.udobj.errorcode.layout_not_init";
    String ERRORCODE_CAN_NOT_DISRELATE = "paas.udobj.errorcode.can_not_disrelate";
    String ERRORCODE_DATA_NO_REFERENCE = "paas.udobj.errorcode.data_no_reference";
    String ERRORCODE_DATA_DELETED = "paas.udobj.errorcode.data_deleted";
    String ERRORCODE_INDEX_NAME_EMPTY = "paas.udobj.errorcode.index_name_empty";
    String ERRORCODE_UNSUPPORT_ACTION = "paas.udobj.errorcode.unsupport_action";
    String ERRORCODE_METADATA_ERROR = "paas.udobj.errorcode.metadata_error";
    String ERRORCODE_LAYOUT_COMPOENNT_ERROR = "paas.udobj.errorcode.layout_compoennt_error";
    String ERRORCODE_CAN_NOT_REF_SELF = "paas.udobj.errorcode.can_not_ref_self";
    String ERRORCODE_OBJECT_NAME_DUPLICATE = "paas.udobj.errorcode.object_name_duplicate";
    String ERRORCODE_LAYOUT_NAME_DUPLICATE = "paas.udobj.errorcode.layout_name_duplicate";
    String ERRORCODE_AUTH_CAL_FAIL = "paas.udobj.errorcode.auth_cal_fail";
    String ERRORCODE_MAX_OBJECT_COUNT = "paas.udobj.errorcode.max_object_count";
    String ERRORCODE_UNSUPPORT_UDOBJ = "paas.udobj.errorcode.unsupport_udobj";
    String ERRORCODE_MAX_LAYOUT_COUNT = "paas.udobj.errorcode.max_layout_count";
    String ERRORCODE_MAX_RECORD_TYPE_COUNT = "paas.udobj.errorcode.max_record_type_count";
    String ERRORCODE_APPRFLOW_PROCESSIG = "paas.udobj.errorcode.apprflow_processing";
    String ERRORCODE_MAX_FIELD_COUNT = "paas.udobj.errorcode.max_field_count";
    String ERRORCODE_LAYOUT_NOT_EXIST = "paas.udobj.errorcode.layout_not_exist";
    String ERRORCODE_DEFAULT_LAYOUT_UNSUPPORT = "paas.udobj.errorcode.default_layout_unsupport";
    String ERRORCODE_MAX_NAME_LENGTH = "paas.udobj.errorcode.max_name_length";
    String ERRORCODE_UDOBJ_NEW_PROJECT_ERROR = "paas.udobj.errorcode.udobj_new_project_error";
    String ERRORCODE_MAX_PRINT_TEMPLATE = "paas.udobj.errorcode.max_print_template";
    String ERRORCODE_TEMPLATE_NAME_DUPLICATE = "paas.udobj.errorcode.template_name_duplicate";
    String ERRORCODE_PAAS_DB_ERROR = "paas.udobj.errorcode.paas_db_error";
    String ERRORCODE_METHOD_FAIL = "paas.udobj.errorcode.method_fail";
    String ERRORCODE_NO_DATA_PERMISSION = "paas.udobj.errorcode.no_data_permission";
    String ERRORCODE_ACTION_NO_PERMISSION = "paas.udobj.errorcode.action_no_permission";
    String ERRORCODE_ROLE_CODE_INVALID = "paas.udobj.errorcode.role_code_invalid";
    String PRIVILEGE_TOO_MANY = "paas.udobj.errorcode.privilege_too_many";
    String ERRORCODE_MAIN_ROLE_NOT_CONFIG = "paas.udobj.errorcode.main_role_not_config";
    String ERRORCODE_RELEVANT_TEAM_UNSUPPORT = "paas.udobj.errorcode.relevant_team_unsupport";
    String FUNC_FAIL = "paas.udobj.func_fail";
    String FUNC_TIMEOUT = "paas.udobj.func_timeout";
    String ONE_FLOW_FAIL = "paas.udobj.one_flow_fail";
    String ONE_FLOW_TIMEOUT = "paas.udobj.one_flow_timeout";
    String VERIFY_FUNC_FAIL = "paas.udobj.verify_func_fail";
    String TEAM_MEMBER = "paas.udobj.team_member";
    String RESPONSIBLE_SUPERIOR = "paas.udobj.responsible_superior";
    String DATA_TEAM_MEMBER_SUPERIOR = "paas.udobj.data_team_member_superior";
    String DATA_MAIN_DEPART_RESPONSIBLE = "paas.udobj.data_main_depart_responsible";
    String DATA_TEAM_DEPART_RESPONSIBLE = "paas.udobj.data_team_depart_responsible";
    String DATA_RESPONSIBLE = "paas.udobj.data_responsible";
    String EXTENSION_FIELD = "paas.udobj.extension_field";
    String DEPARTMENT = "paas.udobj.department";
    String USER_GROUP = "paas.udobj.user_group";
    String USER = "paas.udobj.user";
    String DEPARTMENT_RESIPONSIBLE = "paas.udobj.department_resiponsible";
    String ROLE = "paas.udobj.role";
    String SELECT_ONE_NOT_EMPTY = "paas.udobj.select_one_not_empty";
    String SELECT_MANY_NOT_EMPTY = "paas.udobj.select_many_not_empty";
    /**
     * 字段[{0}]是只读字段，您无权限操作。
     */
    String FIELD_READ_ONLY_NOT_OPERATE = "paas.udobj.field_read_only_not_operate";
    String FIELD_READ_REQUIRED_OPERATE = "paas.udobj.field_required_not_operate";
    String FIELD_DELETED = "paas.udobj.field_deleted";
    String MAPPING_RULE_NOT_EXIST = "paas.udobj.mapping_rule_not_exist";
    String OPERATE_FAIL = "paas.udobj.operate_fail";
    String FIELD_UST_FILL = "paas.udobj.field_ust_fill";
    String FINISH = "paas.udobj.finish";
    String INIT = "paas.udobj.init";
    String CLIENT = "paas.udobj.client";
    String REST_RESPONSE_ERROR = "paas.udobj_rest_response_error";
    String INTELLIFORM_NOTE = "paas.udobj.intelliform_note";
    String INTELLIFORM_ROLE = "paas.udobj.intelliform_role";
    String FIGURE = "paas.udobj.figure";
    String DELETED = "paas.udobj.deleted";
    String INCREASED = "paas.udobj.increased";
    String RESETTED = "paas.udobj.resetted";
    String PRIVILEGE = "paas.udobj.privilege";
    String ADD_REMIND_RECORD_UTIL = "paas.udobj.add_remind_record_util";
    String TEAM_ROLE = "paas.udobj.team_role";
    String USER_DEFINED_ROLE_QUOTA_ERROR = "paas.udobj.user_defined_role_quota_error";
    String STUFF_ROLE_LOG = "paas.udobj.stuff_role_log";
    String VISIT_ADMIN = "paas.udobj.visit_admin";
    String NOT_CRM_ADMIN = "paas.udobj.not_crm_admin";
    String ALLOW_REPEAT_TOOL = "paas.udobj.allow_repeat_tool";
    String USER_DEFINE_ROLE = "paas.udobj.user_define_role";
    String PARAM_EMPTY = "paas.udobj.param_empty";
    String SERVER_ERROR = "paas.udobj.server_error";
    String PARAM_ERROR = "paas.udobj.param_error";
    String QUOTA_LACK = "paas.udobj.quota_lack";
    String QUOTA_FAIL = "paas.udobj.quota_fail";
    String USER_ROLE = "paas.udobj.user_role";
    String USER_MAJOR_ROLE = "paas.udobj.user_major_role";
    String VISIBLE_SCOPE_FAIL = "paas.udobj.visible_scope_fail";
    String ORDER_PASS = "paas.udobj.order_pass";
    String SEARCH_NOTE_SHORT = "paas.udobj.search_note_short";
    String GROUP_MEMBER1 = "paas.udobj.group_member1";
    String GROUP_MEMBER2 = "paas.udobj.group_member2";
    String REPETITION_CHECKER = "paas.udobj.repetition_checker";
    String TARGET_VALUE = "paas.udobj.target_value";
    String ROLE_FUNC_PRIVILEGE = "paas.udobj.role_func_privilege";
    String ROLE_FIELD_PRIVILEGE = "paas.udobj.role_field_privilege";
    String ROLE_NAME = "paas.udobj.role_name";
    String OPEN_CRM_FAIL = "paas.udobj.open_crm_fail";
    String CRM_ALLOWANCE_LACK = "'paas.udobj.crm_allowance_lack";
    String REMOVE_CRM_FAIL = "paas.udobj.remove_crm.fail";
    String INVOKE_BY_EDITION = "paas.udobj.invoke_by_edition";
    String BASE_DATA_PRIVILEGE_INVOKE = "paas.udobj.base_data_privilege_invoke";
    String GET_VERSION = "paas.udobj.get_version";
    String EDITION_SERVICE = "paas.udobj.edition_service";
    String USER_DEFINE_OBJECT_UNSUPPORT = "paas.udobj.user_define_object_unsupport";
    String SUPPORT_USER_DEFINE_OBJECT = "paas.udobj.support_user_define_object";
    String VERSION_QUOTA_INSUFFICIENT = "paas.udobj.version_quota_insufficient";
    String SUPPORT_LAYOUT = "paas.udobj.support_layout";
    String SUPPORT_SERVICE_TYPE = "paas.udobj.support_service_type";
    String SUPPORT_DATA_SHARE = "paas.udobj.support_data_share";
    String SHARE_RULE_MAX = "paas.udobj.share_rule_max";
    String DATA_SHARE_RULE_COUNT_MAX = "paas.udobj.data_share_rule_count_max";
    String ENTITY_SHARE_COUNT_MAX = "paas.udobj.entity_share_count_max";
    String FUNCTION_UPPER_LIMIT = "paas.udobj.function_upper_limit";
    String LAYOUT_RULE_UPPER_LIMIT = "paas.udobj.layout_rule_upper_limit";
    String FUNCTION_NOT_ALLOW = "paas.udobj.function_not_allow";
    String FUNCTION_DEVELOPER_NOT_ALLOW = "paas.udobj.function_develop_not_allow";
    String FUNCTION_UIACTION_NOT_SUPPORT = "paas.udobj.function_uiaction_not_support";
    String FUNCTION_UIACTION_CLIENT_UPGRADE = "paas.udobj.function_uiaction_client_upgrade";
    String NAME_LENGTH_EXCEED = "paas.udobj.name_length_exceed";
    String CREATE_SAVE = "paas.udobj.create_save";
    String EDIT_SAVE = "paas.udobj.edit_save";
    String COMPANY_MESSAGE_GROUP = "paas.udobj.company_message_group";
    String WHOLE = "paas.udobj.whole";
    String MY_RESPONSE = "paas.udobj.my_response";
    String MY_PART = "paas.udobj.my_part";
    String MY_RESPONSE_DEPART = "paas.udobj.my_response_depart";
    String MY_SUBORDINATE_RESPONSE = "paas.udobj.my_subordinate_response";
    String MY_SUBORDINATE_PART = "paas.udobj.my_subordinate_part";
    String SHARE_TO_ME = "paas.udobj.share_to_me";
    String CONVERT = "paas.udobj.convert";
    String OBJECT_MAPPING = "paas.udobj.object_mapping";
    String FIELD_UPDATE = "paas.udobj.field_update";
    String SEND_EMAIL = "paas.udobj.send_email";
    String USER_DEFINE_FUNC = "paas.udobj.user_define_func";
    String NOT_IN_TEAM = "paas.udobj.not_in_team";
    String INEFFECTIVE = "paas.udobj.ineffective";
    String UNDER_REVIEW = "paas.udobj.under_review";
    String NORMAL = "paas.udobj.normal";
    String IN_CHANGE = "paas.udobj.in_change";
    String INVALID = "paas.udobj.action.bulk_invalid";
    String UNKNOW_LIFE_STATUS = "paas.udobj.unknow_life_status";
    String RECEIPT = "paas.udobj.receipt";
    String RECEIPT_AMOUNT = "paas.udobj.receipt_amount";
    String SERVICE_FEE = "paas.udobj.service_fee";
    String PAY_ENTERPRISE_NAME = "paas.udobj.pay_enterprise_name";
    String REMARK = "paas.udobj.remark";
    String PAY_TYPE = "paas.udobj.pay_type";
    String PAY_FINISH_TIME = "paas.udobj.pay_finish_time";
    String TRANSACTION_TIME = "paas.udobj.transaction_time";
    String RELATED_OBJECT = "paas.udobj.related_object";
    String RELATED_OBJECT_NAME = "paas.udobj.related_object_name";
    String RELATED_OBJECT_NAME_UNIQUE = "paas.udobj.related_object_name_unique";
    String STATUS = "paas.udobj.status";
    String RECEIPT_ORDER_NO = "paas.udobj.receipt_order_no";
    String FIELD_LABEL_DUPLICATE = "paas.udobj.field_label_duplicate";
    String FIELD_API_NAME_DUPLICATE = "paas.udobj.field_api_name_duplicate";
    String SIGN_IN_INFO = "paas.udobj.sign_in_info";
    String BEYOND_SCOPE_SIGN_IN = "paas.udobj.beyond_scope_sign_in";
    String BEYOND_SCOPE_SIGN_OUT = "paas.udobj.beyond_scope_sign_out";
    String TIME = "paas.udobj.time";
    String LOCATION = "paas.udobj.location";
    String SIGN_IN_OPTION = "paas.udobj.sign_in_option";
    String SIGN_OUT_OPTION = "paas.udobj.sign_out_option";
    String SIGN_IN = "paas.udobj.sign_in";
    String SIGN_OUT = "paas.udobj.sign_out";
    String SIGN_IN_DEVICE = "paas.udobj.sign_in_device";
    String SYSTEM_RISK = "paas.udobj.system_risk";
    String UNLOCK = "paas.udobj.action.unlock";
    String LOCK = "paas.udobj.lock";
    String NOT_LOCK = "paas.udobj.action.not_lock";

    String END_USING = "paas.udobj.end_using";
    String MENU_ID = "paas.udobj.menu_id";
    String SORT_NUMBER = "paas.udobj.sort_number";
    String ASSOCIATED_OBJECTS = "paas.udobj.associated_objects";
    String MENU_TYPE = "paas.udobj.menu_type";
    String PRESUPPOSITION_OBJECTS = "paas.udobj.presupposition_objects";
    String NOT_OBJECT = "paas.udobj.is_not_object";
    String USER_DEFINE_OBJECT = "paas.udobj.user_define_object";
    String ICON_INDEX = "paas.udobj.icon_index";
    String ICON_PATH = "paas.udobj.icon_path";
    String DEFINE_TYPE = "paas.udobj.define_type";
    String SYSTEM = "paas.udobj.system";
    String WHETHER_HIDDEN = "paas.udobj.whether_hidden";
    String PARENT_ID = "paas.udobj.parent_id";
    String TYPE = "paas.udobj.type";
    String FIELD_TYPE_EMPLOYEE = "paas.udobj.field.type.employee";
    String SHOW_NAME = "paas.udobj.show_name";
    String GROUPING = "paas.udobj.grouping";
    String MENU = "paas.udobj.menu";
    String RESOURCE_ID = "paas.udobj.resource_id";
    String RESOURCE_TYPE = "paas.udobj.resource_type";
    String ROLE_ID = "paas.udobj.role_id";
    String USER_ID = "paas.udobj.user_id";
    String DISPLAY_NAME = "paas.udobj.display_name";
    String BIZ_TYPE = "paas.udobj.biz_type";
    String WEB_END = "pass.udobj.web_end";
    String SORT = "paas.udobj.sort";
    String TENANT_ID = "paas.udobj.tenant_id";
    String DATA_NOT_USED = "paas.udobj.data_not_used";
    String OBJECT_SPECIFY = "paas.udobj.object_specify";
    String FIELD_OBJECT = "paas.udobj.field_object";
    String DEFAULT_BUSINESS_TYPE = "paas.udobj.default_business_type";
    String BUSINESS_TYPE = "paas.udobj.business_type";
    String CREATE_USER = "paas.udobj.create_user";
    String LAST_MODIFY_USER = "paas.udobj.last_modify_user";
    String CREATE_TIME = "paas.udobj.create_time";
    String LAST_MODIFY_TIME = "paas.udobj.last_modify_time";
    String WHETHER_INVALID = "paas.udobj.whether_invalid";
    String FIELD_SPECIFY = "paas.udobj.field_specify";
    String VALIDATION_RULE = "paas.udobj.validation_rule";
    String NEED_DEAL_WITH = "paas.flow.bpm.task.in_progress";
    String HAVE_DONE = "paas.flow.bpm.task.pass";
    String APPROVAL_HAVE_DONE = "paas.flow.approval.task.pass";
    String APPROVAL_NEED_DEAL_WITH = "paas.flow.approval.task.in_progress";
    String STAGE_HAVE_DONE = "paas.flow.stage.task.pass";
    String STAGE_NEED_DEAL_WITH = "paas.flow.stage.task.in_progress";
    String TASK_DELEGATE_IN_PROGRESS = "paas.task.delegate.in_progress";//待处理
    String TASK_DELEGATE_PASS = "paas.task.delegate.pass";//已处理
    String TASK_DELEGATE_ALL = "paas.task.delegate.all";//全部
    String UNSOLVED_WORK_ORDER = "paas.udobj.unsolved_work_order";
    String URGENT_WORK_ORDER = "paas.udobj.urgent_work_order";
    String CLOSED_WORK_ORDER = "paas.udobj.closed_work_order";
    String ALREADY_TERMINATED = "paas.flow.bpm.task.cancel";
    String EXCEPTION = "paas.flow.bpm.instance.error";
    String TASK_EXCEPTION = "paas.flow.bpm.task.error";
    String UNDER_WAY = "paas.flow.bpm.instance.in_progress";
    String COMPLETED = "paas.flow.bpm.instance.pass";
    String TERMINATED = "paas.flow.bpm.instance.cancel";
    String APPROVAL_FLOW = "paas.udobj.approval_flow";
    String BUSINESS_FLOW = "paas.udobj.business_flow";
    String WORK_FLOW = "paas.udobj.work_flow";
    String STAGE_PROPELLER = "paas.udobj.stage_propeller";
    String FREE_PROCESS = "paas.udobj.free_process";
    String FREE_APPROVAL_PROCESS = "paas.udobj.free_approval_process";
    String CALL_CENTER_TEMPORARY_RIGHTS_SCENE = "paas.udobj.call_center_temporary_rights_scene";
    String UNLOAD = "paas.udobj.unload";
    String CHANNEL_MANAGER = "paas.udobj.channel_manager";
    String CHANNEL_MANAGER_COMPANY = "paas.udobj.channel_manager_company";
    String AGENTS = "paas.udobj.agents";
    String DOWN_PRM_AGENTS = "paas.udobj.down_prm_agents";
    String CUSTOMER_TRANSACTION_OPERATOR = "paas.udobj.customer_transaction_operator";
    String DISTRIBUTION_CUSTOMER_TRANSACTION_PERMISSION = "paas.udobj.distribution_customer_transaction_permission";

    String MEMBER_MANAGER = "paas.udobj.member_manager";
    String MEMBER_MANAGER_DESCRIPTION = "paas.udobj.member_manager_description";

    String NAME = "paas.udobj.name";
    String MAIN_DEPARTMENT = "paas.udobj.main_department";
    String POSITION = "paas.udobj.position";
    String MASTER_ROLE = "paas.udobj.master_role";
    String STAFF_ROLE_DISTRIBUTION = "paas.udobj.staff_role_distribution";
    String STAFF_ROLE = "paas.udobj.staff_role";
    String PAYMENT = "paas.udobj.payment";
    String BUSINESS_PROCESS_INSTANCE = "paas.udobj.business_process_instance";
    String APPROVAL_INSTANCE = "paas.udobj.approval_instance";

    //    String DATA_NOT_FIND = "paas.udobj.paas.udobj.data_not_find";
    String OBJECT_DATA_NOT_FOUND = "paas.udobj.object_data_not_found";
    String RELATED_MUSTER_UNLIKE = "paas.udobj.related_muster_unlike";
    String DATA_LOCKED = "paas.udobj.data_locked";
    String USER_NO_DATA_PRIVILEGE = "paas.udobj.user_no_data_privilege";
    String DUPLICATE_SEARCH_DATA_TIMEOUT = "paas.udobj.duplicate_search_data_timeout";
    String NOT_FIND_OBJECT = "paas.udobj.not_find_object";
    String NOT_SIGN_OUT = "paas.udobj.not_sign_out";
    String BUTTON_IS_DISABLE_OR_DELETE = "paas.udobj.button_is_disable_or_delete";
    String MUST_FILL_IN = "paas.udobj.must_fill_in";
    //唯一性标识
    String UNIQUE_FILL_IN = "paas.udobj.unique_fill_in";
    String TEAM_MEMBER_CHANGE = "paas.udobj.team_member_change";
    //    String NOT_CHANGE_DETAIL_OBJECT_RELATED_MEMBER = "paas.udobj.not_change_detail_object_related_member";
    String OPERATION_FAILED = "paas.udobj.operation_failed";
    String FAILED_DATA = "paas.udobj.failed_data";
    String USER_NO_IMPORT_PRIVILEGE = "paas.udobj.user_no_import_privilege";
    String NO_PRIVILEGE_IMPORT_FIELDS = "paas.udobj.no_privilege_import_fields";
    String EXCEL_COLUMN_AND_TEMPLATE_DIFFERENT = "paas.udobj.excel_column_and_template_different";
    String EXCEL_COLUMN_AND_TEMPLATE_DIFFERENT_1 = "paas.udobj.excel_column_and_template_different_1";
    //Excel中的{0}错误。
    String EXCEL_CONFIG_ERROR = "paas.udobj.excel_config_error";
    //导入管控设置查找关联字段{0}配置的字段{1}不唯一，请联系管理员重新配置。
    String EXCEL_OBJECT_REFERENCE_ERROR = "paas.udobj.excel_object_reference_error";
    //导入管控设置查找关联字段{0}配置的唯一性字段{1}不存在，请联系管理员重新配置。
    String EXCEL_OBJECT_REFERENCE_FIELD_NOT_EXIST = "paas.udobj.excel_object_reference_field_not_exist";
    //导入管控设置查找关联字段关联的对象{0}不存在，请联系管理员重新配置。
    String EXCEL_OBJECT_REFERENCE_OBJECT_NOT_EXIST = "paas.udobj.excel_object_reference_object_not_exist";
    String IOS_PRISON_BREAK = "paas.udobj.ios_prison_break";
    String ANDROID_CHEAT_SOFTWARE = "paas.udobj.android_cheat_software";
    String ANDROID_IP_ADDRESS_FORGERY = "paas.udobj.android_ip_address_forgery";
    String SIMULATOR = "paas.udobj.simulator";
    String NOT_FIND_SIGN_IN = "paas.udobj.not_find_sign_in";
    String DATA_NOT_FIND_OR_DELETE = "paas.udobj.data_not_find_or_delete";
    String CANCELLED_ROLE_IDENTITY = "paas.udobj.cancelled_role_identity";
    String CANCELLED_ROLE_IDENTITY_2 = "paas.udobj.cancelled_role_identity_2";
    String CANCELLED_ROLE_IDENTITY_3 = "paas.udobj.cancelled_role_identity_3";
    String CANCELLED_ROLE_IDENTITY_4 = "paas.udobj.cancelled_role_identity_4";
    String CANCELLED_ROLE_IDENTITY_5 = "paas.udobj.cancelled_role_identity_5";
    String CANCELLED_ROLE_IDENTITY_6 = "paas.udobj.cancelled_role_identity_6";
    String CANCELLED_ROLE_IDENTITY_7 = "paas.udobj.cancelled_role_identity_7";
    String CANCELLED_ROLE_IDENTITY_8 = "paas.udobj.cancelled_role_identity_8";
    String CANCELLED_ROLE_IDENTITY_9 = "paas.udobj.cancelled_role_identity_9";
    String CANCELLED_ROLE_IDENTITY_10 = "paas.udobj.cancelled_role_identity_10";
    String CANCELLED_ROLE_IDENTITY_11 = "paas.udobj.cancelled_role_identity_11";
    String OWNER_CAN_NOT_DELETE = "paas.udobj.owner_can_not_delete";
    String OTHER_AND_SYMBOL = "paas.udobj.other_and_symbol";
    String DO_NOT_INPUT_DUPLICATE_PRODUCT = "paas.udobj.do_not_input_duplicate_product";
    String UNKNOWN_EXCEPTION = "paas.udobj.unknown_exception";
    /**
     * {0} 部门不存在或已停用！
     */
    String DEPARTMENT_NOT_FIND = "paas.udobj.department_not_find";
    String DEPARTMENT_CODE_NOT_FIND = "paas.udobj.department_code_not_find";
    String MEMBER_NOT_FIND = "paas.udobj.member_not_find";
    String MEMBER_NOT_FIND_2 = "paas.udobj.member_not_find_2";
    String DUPLICATE_PERSON_NAME = "paas.udobj.duplicate_person_name";
    String CONTENT_NOT_UNIQUE = "paas.udobj.content_not_unique";
    String DO_NOT_ADD_DUPLICATE_PRODUCT_IN_SINGLE_PRICE_BOOK = "paas.udobj.do_not_add_duplicate_product_in_single_price_book";
    String ROW_VALUE_DUPLICATE = "paas.udobj.row_value_duplicate";
    String RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE = "paas.udobj.related_object_data_delete_or_no_privilege";
    String DATA_ID_MUST_FILL = "paas.udobj.data_id_must_fill";
    String REFERENCE_NAME_DUPLICATE = "paas.udobj.related_object_data_duplicated";
    //查找关联字段{0}指定的唯一性字段不存在，请检查后台导入配置
    String REFERENCE_TARGET_FIELD_NOT_EXIST = "paas.udobj.reference_target_field_not_exist";
    //查找关联字段{0}指定的唯一性字段不唯一，请在后台导入配置重新配置
    String REFERENCE_TARGET_FIELD_NOT_UNIQUE = "paas.udobj.reference_target_field_not_unique";
    String UNKNOWN_ERROR = "paas.udobj.unknown_error";
    String OPTION_FALL_SHORT_SELECT_ONT_RULE = "paas.udobj.option_fall_short_select_ont_rule";
    String PLEASE_INPUT = "paas.udobj.please_input";
    String COUNTRY = "paas.udobj.country";
    String PROVINCE = "paas.udobj.province";
    String CITY = "paas.udobj.city";
    String DISTRICT = "paas.udobj.district";
    String NO_THIS = "paas.udobj.no_this";
    String OPTION_NOT_USED_CAN_NOT_IMPORT = "paas.udobj.option_not_used_can_not_import";
    String OTHER = "paas.udobj.other";
    String OTHER_OPTION_CAN_NOT_IS_NULL = "paas.udobj.other_option_can_not_is_null";
    String OTHER_OPTION_ONLY_EXIST_ONE = "paas.udobj.other_option_only_exist_one";
    String MUST_WITHIN_LIMITS = "paas.udobj.must_within_limits";
    String MUST_IS_ONE_OF = "paas.udobj.must_is_one_of";
    String CONTENT_TOO_LONG = "paas.udobj.content_too_long";
    String LONG_TEXT_FORMAT_ERROR = "paas.udobj.long_text_format_error";
    String DATE_TIME_FORMAT_ERROR = "paas.udobj.date_time_format_error";
    String DATE_FORMAT_ERROR = "paas.udobj.date_format_error";
    String FORMAT_ERROR_PLEASE_INPUT_AMOUNT = "paas.udobj.format_error_please_input_amount";
    String FORMAT_ERROR_PLEASE_INPUT_INTEGER = "paas.udobj.format_error_please_input_integer";
    String FORMAT_ERROR_PLEASE_INPUT_DECIMAL = "paas.udobj.format_error_please_input_decimal";
    /**
     * {0}格式错误！请填入一个可解析的数字。
     * {0} format error!Please give String is a parsable number.
     */
    String FORMAT_ERROR_PLEASE_INPUT_PARSABLE = "pass.udobj.format_error_please_input_parsable";
    /**
     * {0}格式错误！请填入一个整数位数和小数位的总长度不超过{1}位的数字。
     * {0} format error! Please fill in a number whose total length of integer digits and decimal digits does not exceed {1} digits.
     */
    String FORMAT_ERROR_PLEASE_INPUT_NUMBER_MAX_LENGTH = "pass.udobj.format_error_please_input_number_max_length";
    /**
     * {0}格式错误！请填写一个整数的百分数（不大于{1}位）。
     * {0} Format error! Please fill in an integer percentage (not more than {1} digits).
     */
    String FORMAT_ERROR_PLEASE_INPUT_PERCENTILE_INTEGER = "pass.udobj.format_error_please_input_percentile_integer";
    /**
     * {0}格式错误！请填写小数（整数部分不大于{1}位，小数部分不大于{2}位。
     * {0} Format error! Please fill in the decimal the integer part should not exceed {1} digits, and the decimal part should not exceed {2} digits.
     */
    String FORMAT_ERROR_PLEASE_INPUT_PERCENTILE_DECIMAL = "pass.udobj.format_error_please_input_percentile_decimal";
    String TRUE = "paas.udobj.true";
    String FALSE = "paas.udobj.false";
    String FORMAT_ERROR_ONLY_INPUT_TRUE_OR_FALSE_OR_OPTION = "paas.udobj.format_error_only_input_true_or_false_or_option";
    String FORMAT_ERROR = "paas.udobj.format_error";
    String TIME_FORMAT_ERROR = "paas.udobj.time_format_error";
    String FORMAT_ERROR_ONLY_INPUT_NUMBER_AND_SIGN_LESS_THAN_100_BYTE = "paas.udobj.format_error_only_input_number_and_sign_less_than_100_byte";
    String OBJECT_MUSTER_OBJECT_NOT_UNIQUE = "paas.udobj.object_muster_object_not_unique";
    String NUMBER_ALREADY_EXIST_PLEASE_IMPORT_AGAIN = "paas.udobj.number_already_exist_please_import_again";
    String AUTO_NUMBER_ALREADY_EXIST_PLEASE_IMPORT_AGAIN = "paas.udobj.auto_number_already_exist_please_import_again";
    String OPERATER_NO_USED_RECORD_TYPE = "paas.udobj.operater_no_used_record_type";
    String RECORD_TYPE_IS_DISABLED = "paas.udobj.record_type_is_disabled";
    String RECORD_TYPE_IS_DELETE_OR_OPERATOR_NO_PRIVILEGE_CREAT_THIS_RECORD_TYPE = "paas.udobj.record_type_is_delete_or_operator_no_privilege_creat_this_record_type";
    String RECORD_TYPE_IS_NOT_EXIST = "paas.udobj.record_type_not_exit";
    String CHANGE_OWNER_SUCCESS = "paas.udobj.change_owner_success";
    //    String OBJECT_IS_DISABLE_CAN_NOT_OPERATION = "paas.udobj.object_is_disable_can_not_operation";
    String OBJECT_DESCRIBE_DIABLED = "paas.udobj.object_describe_diabled";
    String CAN_NOT_ONLY_CHANGE_DETAIL_OBJECT_OWNER = "paas.udobj.can_not_only_change_detail_object_owner";
    String CAN_NOT_ONLY_CHANGE_DETAIL_OBJECT_OUT_OWNER = "paas.udobj.can_not_only_change_detail_object_out_owner";
    String ALREADY_EXIST_APPROVAL_FLOW = "paas.udobj.already_exist_approval_flow";
    String OWNER_IS_NULL = "paas.udobj.owner_is_null";
    String ID_IS_NOT_FIND_CAN_NOT_CHANGE_OWNER = "paas.udobj.id_is_not_find_can_not_change_owner";
    String IS_DELETED_CAN_NOT_CHANGE_OWNER = "paas.udobj.is_deleted_can_not_change_owner";
    String CHANGE_OWNER_ERROR = "paas.udobj.change_owner_error";
    String SYSTEM_EXCEPTION = "paas.udobj.system_exception";
    String MASTER_DETAIL_FIELD = "paas.udobj.master_detail_field";
    String MASTER_DETAIL_OPERATION_FAILED = "paas.udobj.master_detail_operation_failed";
    String MASTER_DETAIL_OPERATION_FAILED_SYSTEM_EXCEPTION = "paas.udobj.master_detail_operation_failed_system_exception";
    String MASTER_OBJECT_IS_LOCK_CAN_NOT_UNLOCK_OPERATION_DATA = "paas.udobj.master_object_is_lock_can_not_unlock_operation_data";
    String EDIT_OR_CUSTOM_BUTTON_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION = "paas.udobj.edit_or_custom_button_approval_flow_can_not_operation_data_should_unlock_operation";
    String REMOVE_PARTNER_SUCCESS = "paas.udobj.remove_partner_success";
    //    String DATA_ID_CAN_NOT_IS_NULL = "paas.udobj.data_id_can_not_is_null";
    String DATA_ID_EMPTY = "paas.udobj.data_id_empty";
    String IS_INVALID_CAN_NOT_OPERATION = "paas.udobj.is_invalid_can_not_operation";
    String IS_LOCK_CAN_NOT_OPERATION = "paas.udobj.is_lock_can_not_operation";
    String IN = "paas.udobj.in";
    String REMOVE = "paas.udobj.remove";
    String ONLY_DEMO_FORMAT_EQULE_CAN_IMPORT_ELSE_OTHER_FORMAT_CAN_NOT_IMPORT = "paas.udobj.only_demo_format_equle_can_import_else_other_format_can_not_import_";
    String ONLY_DEMO_FORMAT_EQULE_CAN_IMPORT = "paas.udobj.only_demo_format_equle_can_import";
    String SAMPLE_TEXT = "paas.udobj.sample_text";
    String SAMPLE_BIG_TEXT = "paas.udobj.sample_big_text";
    String OPTION_ONE_OPTION_TWO = "paas.udobj.option_one_option_two";
    String PERSONNEL_NAME = "paas.udobj.personnel_name";
    String PERSONNEL_NAME_CODE = "paas.udobj.personnel_name_code";
    String PERSONNEL_NAME_MULTI = "paas.udobj.personnel_name_multi";
    String DEPARTMENT_NAME = "paas.udobj.department_name";
    String DEPARTMENT_NAME_CODE = "paas.udobj.department_name_code";
    String ORGANIZATION_NAME = "paas.udobj.organization_name";
    String ORGANIZATION_NAME_CODE = "paas.udobj.organization_name_code";
    String MASTER_NAME = "paas.udobj.master_name";
    String DEFAULT_RECORD_TYPE = "paas.udobj.default_record_type";
    String PRIMARY_OPTION_OR_SECONDARY_OPTION = "paas.udobj.primary_option_or_secondary_option";
    String LOCATION_INFORMATION = "paas.udobj.location_information";
    String CHINA = "paas.udobj.china";
    String BEI_JING = "paas.udobj.bei_jing";
    String HAI_DIAN = "paas.udobj.hai_dian";
    String PATH_SAMPLE = "paas.udobj.import_path_sample";
    String OBJECT_EXIST_MD_RELATION_CAN_NOT_BATCH_CREAT = "paas.udobj.object_exist_MD_relation_can_not_batch_creat";
    String DATA_VALIDATE_FAILED_PLEASE_AGAIN = "paas.udobj.data_validate_failed_please_again";
    String DATA_INIT_EXCPRATION_PLEASE_AGAIN = "paas.udobj.data_init_excpration_please_again";
    String RECORD_TYPE_NOT_EXIST = "paas.udobj.record_type_not_exist";
    String OBJECT_API_NAME_CAN_NOT_IS_NULL = "paas.udobj.object_api_name_can_not_is_null";
    String DELETE_DATA_CAN_NOT_IS_NULL = "paas.udobj.delete_data_can_not_is_null";
    String OBJECT_ID_ALREADY_EXIST_APPROVAL_FLOW = "paas.udobj.object_id_already_exist_approval_flow";
    String NOW_ONLY_EXPORT_HUNDRED_THOUSAND_DATA_BY_ONCE = "paas.udobj.now_only_export_hundred_thousand_data_by_once";
    String EXPORT_EXCEED_THROTTLE = "paas.udobj.export_exceed_throttle";
    String LIST_SPECIFY = "paas.udobj.list_specify";
    String EXPORT_FILE_NAME = "paas.udobj.export_file_name";
    String FRONT_VALIDATE_FUNCTION_FAILED = "paas.udobj.front_validate_function_failed";
    String DETAIL_DATA_NUMBER_MORE = "paas.udobj.detail_data_number_more";
    String RELATED_DATA_NUMBER_MORE = "paas.udobj.related_data_number_more";
    String CREAT_MASTER_DATA_DETAIL_DATA_CAN_NOT_IS_NULL = "paas.udobj.creat_master_data_detail_data_can_not_is_null";
    String DETAIL_OBJECT_IS_NOT_FIND_MD_FIELD = "paas.udobj.detail_object_is_not_find_MD_field";
    String DATA_INVALID_FAILED = "paas.udobj.data_invalid_failed";
    String DATA_INVALID_FAILED_DETAIL = "paas.udobj.data_invalid_failed_detail";
    String ONGOING_APPROVAL_FLOW_AND_NO_CURRENT_OPERATION_POSSIBLE = "paas.udobj.ongoing_approval_flow_and_no_current_operation_possible";
    String MD_INVALID_DETAIL_DATA_TIME_OUT = "paas.udobj.MD_invalid_detail_data_time_out";
    String OBJECT_APPROVAL_FLOW_FAILED = "paas.udobj.object_approval_flow_failed";
    String ONLY_INSERT_ORDINARY_TEAM_MEMBER_ROLE = "paas.udobj.only_insert_ordinary_team_member_role";
    String TEAM_MEMBER_ROLE_DISABLE_OR_NOT_EXIST = "paas.udobj.team_member_role_disable_or_not_exist";
    String PERSONNEL_PRIVILEGE_DEPLOY_ERROR = "paas.udobj.personnel_privilege_deploy_error";
    String ADD_ROLE = "paas.udobj.add_role";
    String ADD_ROLE_2 = "paas.udobj.add_role_2";
    String ADD_ROLE_3 = "paas.udobj.add_role_3";
    String ADD_ROLE_4 = "paas.udobj.add_role_4";
    String ADD_ROLE_5 = "paas.udobj.add_role_5";
    String ADD_ROLE_6 = "paas.udobj.add_role_6";
    String ADD_ROLE_7 = "paas.udobj.add_role_7";
    String ADD_ROLE_8 = "paas.udobj.add_role_8";
    String ADD_ROLE_9 = "paas.udobj.add_role_9";
    String ADD_ROLE_10 = "paas.udobj.add_role_10";
    String ADD_ROLE_11 = "paas.udobj.add_role_11";
    String OPERATER_FAILED_REASON_CON_NOT_ADD_OWNER = "paas.udobj.operater_failed_reason_con_not_add_owner";
    String MASTER_IS_INVALID_PLEASE_RECOVER_MASTER_OBJECT = "paas.udobj.master_is_invalid_please_recover_master_object";
    String MD_RECOVER_DETAIL_DATA_TIME_OUT = "paas.udobj.MD_recover_detail_data_time_out";
    String GOODS_RECEIVED_NOTE_GOODS_RECEIVED_NOTE_PRODUCT = "paas.udobj.goods_received_note_goods_received_note_product";
    String DELIVERY_NOTE_DELIVERY_NOTE_PRODUCT = "paas.udobj.delivery_note_delivery_note_product";
    String OUT_NOTE_OUT_NOTE_PRODUCT = "paas.udobj.out_note_out_note_product";
    String OPERATION_PARAM_AND_FUNCTION_PARAM_IS_DIFFERENT = "paas.udobj.operation_param_and_function_param_is_different";
    String OPERATION_PARAM_AND_FUNCTION_PARAM_IS_DIFFERENT_FUNCTION_PARAM = "paas.udobj.operation_param_and_function_param_is_different_function_param";
    String BUTTON_NAME_DUPLICATE = "paas.udobj.button_name_duplicate";
    String FUNCTION_ALREADY_USED = "paas.udobj.function_already_used";
    String ASSIGN = "paas.udobj.assign";
    String UNASSIGN = "paas.udobj.unassign";
    String VALIDITY_CAN_NOT_IS_NULL = "paas.udobj.validity_can_not_is_null";
    String VERIFY_OBJECT_EXIST_AND_SAVE_SECCESS_END_CASE_ADD_CALC_FORMULA = "paas.udobj.verify_object_exist_and_save_seccess_end_case_add_calc_formula";

    String EXPRESSION_STACK_OVERFLOW = "paas.udobj.expression_stack_overflow";
    String FORMULA_SYNTAX_ERROR = "paas.udobj.formula_syntax_error";
    String PARAM_ERROR_OBJECT_API_NAME_IS_NULL = "paas.udobj.param_error_object_api_name_is_null";
    String OBJECT_DATA_CAN_NOT_IS_NULL = "paas.udobj.object_data_can_not_is_null";
    String NO_COUNT_FIELD_NEED_DEAL = "paas.udobj.no_count_field_need_deal";
    String FIELD_API_NAME_CAN_NOT_IS_NULL = "paas.udobj.field_api_name_can_not_is_null";
    String NO_FIELD_NEED_DEAL = "paas.udobj.no_field_need_deal";
    String DATA_NOT_SATISFIED_CREAT_CONDITION = "paas.udobj.data_not_satisfied_creat_condition";
    String FIELD_DEPEND_RELATED_PLEASE_ADD_DEPEND_FIELD = "paas.udobj.field_depend_related_please_add_depend_field";
    String OBJECT = "paas.udobj.object";
    String BUTTON_CANNOT_CREATE = "paas.udobj.button_cannot_create";
    String DETAIL_BUTTON_USE_PAGE = "paas.udobj.detail_button_use_page";
    String CREATE_BUTTON_USE_PAGE = "paas.udobj.create_button_use_page";
    String EDIT_BUTTON_USE_PAGE = "paas.udobj.edit_button_use_page";
    String LIST_BUTTON_USE_PAGE = "paas.udobj.list_button_use_page";
    String RELATED_BUTTON_USE_PAGE = "paas.udobj.related_button_use_page";
    String LIST_NORMAL_BUTTON_USE_PAGE = "paas.udobj.list_normal_button_use_page";
    String LIST_BATCH_BUTTON_USE_PAGE = "paas.udobj.list_batch_button_use_page";
    String DETAIL_BUTTON_USE_PAGE_MSG = "paas.udobj.detail_button_use_page_msg";
    String CREATE_BUTTON_USE_PAGE_MSG = "paas.udobj.create_button_use_page_msg";
    String EDIT_BUTTON_USE_PAGE_MSG = "paas.udobj.edit_button_use_page_msg";
    String LIST_BUTTON_USE_PAGE_MSG = "paas.udobj.list_button_use_page_msg";
    String RELATED_BUTTON_USE_PAGE_MSG = "paas.udobj.related_button_use_page_msg";
    String LIST_NORMAL_BUTTON_USE_PAGE_MSG = "paas.udobj.list_normal_button_use_page_msg";
    String LIST_BATCH_BUTTON_USE_PAGE_MSG = "paas.udobj.list_batch_button_use_page_msg";
    String LIST_RELATED_BUTTON_USE_PAGE = "paas.udobj.list_related_button_use_page";
    String CANNOT_CREATE = "paas.udobj.cannot_create";
    String DUPLICATE_SEARCH_RULE_ACTIVE = "paas.udobj.duplicate_search_rule_active";
    //    String LEADS = "paas.udobj.leads";
//    String ACCOUNT = "paas.udobj.account";
//    String CONTACT = "paas.udobj.contact";
//    String OPPORTUNITY = "paas.udobj.opportunity";
//    String SALES_ORDER = "paas.udobj.sales_order";
//    String RETURNED_GOOD_INVOICE = "paas.udobj.returned_good_invoice";
//    String REFUND = "paas.udobj.refund";
//    String INVOICE_APPLICATION = "paas.udobj.invoice_application";
//    String PRODUCT = "paas.udobj.product";
//    String VISITING = "paas.udobj.visiting";
//    String MARKETING_EVENT = "paas.udobj.marketing_event";
//    String CONTRACT = "paas.udobj.contract";
    String MANAGEMENT = "paas.udobj.management";
    String FIELD_MANAGEMENT = "paas.udobj.field_management";
    String SALES_CLUE_POOL = "paas.udobj.sales_clue_pool";
    String FUNCTION_PRIVILEGE_MANAGEMENT = "paas.udobj.function_privilege_management";
    String RULE_SETTING = "paas.udobj.rule_setting";
    String SALE_FLOW_SETTING = "paas.udobj.sale_flow_setting";
    String HIGH_SEAS = "paas.udobj.high_seas";
    String PRODUCT_SORT = "paas.udobj.product_sort";
    String DATA_PRIVILEGE_MANAGEMENT = "paas.udobj.data_privilege_management";
    String DATA_BOARD_SETTING = "paas.udobj.data_board_setting";
    String SALE_REPORT_SETTING = "paas.udobj.sale_report_setting";
    String APPROVAL_FLOW_MANAGER = "paas.udobj.approval_flow_manager";
    String WORK_FLOW_MANAGER = "paas.udobj.work_flow_manager";
    String BACKLOG_REMOVAL_TOOL = "paas.udobj.backlog_removal_tool";
    String TASK_DELEGATE_SETTING = "paas.udobj.task_delegate_setting";
    String CUSTOM_OBJECT_MANAGER = "paas.udobj.custom_object_manager";
    String CONVERSION_RULE_GENERATION_ERROR = "paas.udobj.conversion_rule_generation_error";
    String CONVERSION_RULE_CORRESPONDING_BUTTON_GENERATION_ERROR = "paas.udobj.conversion_rule_corresponding_button_generation_error";
    String BUTTON_INFO_NOT_EXIST = "paas.udobj.button_info_not_exist";
    String BUTTON_NAME_CANNOT_EMPTY = "paas.udobj.button_name_cannot_empty";
    String MAPPING_RULE_CORRESPONDING_BUTTON_DOES_NOT_EXIST = "paas.udobj.mapping_rule_corresponding_button_does_not_exist";
    String COMPLETED_PAYMENT = "paas.udobj.completed_payment";
    String COLLECTION_AMOUNT_IS_EMPTY = "paas.udobj.collection_amount_is_empty";
    String COLLECTION_AMOUNT_IS_ILLEGAL = "paas.udobj.collection_amount_is_illegal";
    String DATA_LIFE_STATUS_IS_NOT_IN_EFFECT = "paas.udobj.data_life_status_is_not_in_effect";
    String DATA_HAS_been_locked = "paas.udobj.data_has_been_locked";
    String THE_FOLLOWING_ARE = "paas.udobj.the_following_are";
    String DATA_IS_NOT_locked = "paas.udobj.data_is_not_locked";
    String DATA_HAS_BEEN_LOCKED_PLEASE_UNLOCK = "paas.udobj.data_has_been_locked_please_unlock";
    String OBJECT_DESCRIBE_IS_EMPTY = "paas.udobj.object_describe_is_empty";
    String RECORD_TYPE_IS_EMPTY = "paas.udobj.record_type_is_empty";
    String MISSING_PRIMARY_ROLE = "paas.udobj.missing_primary_role";
    String GLOBAL_VARIABLE_UNMODIFY = "paas.udobj.global_variable_unmodify";
    String GLOBAL_VARIABLE_NOT_EXIST_OR_DELETED = "paas.udobj.global_variable_not_exist_or_deleted";
    String CURRENT_TIME = "paas.udobj.current_time";
    String CURRENT_DATE = "paas.udobj.current_date";
    String CURRENT_DATE_TIME = "paas.udobj.current_date_time";
    String SYSTEM_CREATION = "paas.udobj.system_creation";
    String IMPORT_TEMPLATE = "paas.udobj.import_template";
    String REQUIRED = "paas.udobj.required";
    String YES = "paas.udobj.yes";
    String NO = "paas.udobj.no";
    String BUTTON_OBJECT = "paas.udobj.button_object";
    String BUTTON_NOT_EXIST_OR_DELETED = "paas.udobj.button_not_exist_or_deleted";
    String CUSTOM_BUTTON = "paas.udobj.custom_button";

    String DUPLICATE_SEARCH_RULE = "paas.udobj.duplicate_search_rule";
    String BUTTON_PARA_CANNOT_EXCEED = "paas.udobj.button_para_cannot_exceed";
    String COPY = "paas.udobj.copy";
    String RECEIVABLES_RECORD = "paas.udobj.receipt";
    String TOP_INFO = "paas.udobj.top_info";
    String SUMMARY = "paas.udobj.summary";
    String SERVICE_RECORD = "paas.udobj.service_record";
    String SALES_RECORD = "paas.udobj.SALES_record";
    String PROCESS_LIST = "paas.udobj.process_list";
    String APPROVALFLOW = "paas.udobj.approvalflow";
    String ANNEX = "paas.udobj.annex";
    String CHARGE = "paas.udobj.charge";
    String MAIL = "paas.udobj.mail";
    String NAME_POSITION = "paas.udobj.name_position";
    String RELATED_TEAM = "paas.udobj.constant.relevant_team";
    String UNSUPPORTED_COUNT_TYPE = "paas.udobj.unsupported_count_type";
    String COUNT_FIELD = "paas.udobj.count_field";
    String RECALCULATION_CONDITION_IS_ILLEGAL = "paas.udobj.recalculation_condition_is_illegal";
    String PREFIX_IS_TOO_LONG = "paas.udobj.prefix_is_too_long";
    String SUFFIX_IS_TOO_LONG = "paas.udobj.suffix_is_too_long";
    String AUTONUMBER_PREFIX_LENGTH_CHECK_FAILED = "paas.udobj.autonumber_prefix_length_check_failed";
    String AUTONUMBER_SUFFIX_LENGTH_CHECK_FAILED = "paas.udobj.autonumber_suffix_length_check_failed";
    String AUTONUMBER_SERIAL_NUMBER_LENGTH_CHECK_FAILED = "paas.udobj.autonumber_serial_number_length_check_failed";
    String AUTONUMBER_CONDITION_CHECK_FAILED = "paas.udobj.autonumber_condition_check_failed";
    String UPLOAD_FILE_FAILED = "paas.udobj.upload_file_failed";
    String SAVE_FILE_FAILED = "paas.udobj.save_file_failed";
    String SAVE_IMAGE_FAILED = "paas.udobj.save_image_failed";
    String LAYOUT_OBJECT = "paas.udobj.layout_object";
    String LAYOUT_NOT_EXIST_OR_DELETED = "paas.udobj.layout_not_exist_or_deleted";
    String DEFAULT_LAYOUT_CANNOT_BE_DELETED = "paas.udobj.default_layout_cannot_be_deleted";
    String OF_NOT_EMPTY = "paas.udobj.of_not_empy";
    String DETAILS = "paas.udobj.details";
    String OTHER_INFO = "paas.udobj.other_info";
    String DATA_PERMISSION_WITHOUT_THIS_OPERATION = "paas.udobj.data_permission_without_this_operation";
    String UNSATISFIED_CONDITION = "paas.udobj.unsatisfied_condition";
    String LOOKUP_SINGLE_UNSATISFIED_CONDITION = "paas.udobj.lookup_single_unsatisfied_condition";
    String LOOKUP_UNSATISFIED_CONDITION = "paas.udobj.lookup_unsatisfied_condition";
    String LOOKUP_MULTIPLE_UNSATISFIED_CONDITION = "paas.udobj.lookup_multiple_unsatisfied_condition";
    String DATA = "paas.udobj.data";
    String EXPORT_DATA = "paas.udobj.export_data";
    String BASIC_INFO = "paas.udobj.basic_info";
    String DATA_REVOKED_OR_DELETED = "paas.udobj.data_revoked or deleted";
    String DATA_EXPORT_RECOMMENDATIONS = "paas.udobj.data_export_recommendations";
    String EXECUTE_RESULT_FAIL = "paas.udobj.execute_result_fail";
    String EXECUTE_RESULT = "paas.udobj.execute_result";
    String EXECUTE_RESULT_DETAIL = "paas.udobj.execute_result_detail";
    String DELETE_RESULT_DETAIL = "paas.udobj.delete_object_fail";
    String DATA_IN_INVALID_OR_DELETE = "paas.udobj.data_in_invalid_or_delete";
    String RELIEVE_ASSOCIATED_OBJECTS = "paas.udobj.relieve_associated_objects";
    String UNABLE_TO_REMOVE_ASSOCIATION = "paas.udobj.unable_to_remove_association";
    String UNSUPPORT_INVOKING = "Paas.udobj.unsupport_invoking";
    String REQUEST_PARAM_IS_NULL = "paas.udobj.request_param_is_null";
    String SORT_NUMBER_CANNOT_BE_NULL = "paas.udobj.sort_number_cannot_be_null";
    String WHETHER_SYSTEM_MENU = "paas.udobj.whether_system_menu";
    String IS_PREFABRICATION_MENU = "paas.udobj.is_prefabrication_menu";
    String ENABLED_STATE = "paas.udobj.enabled_state";
    String BELONG_APPLICATION_MODULE = "paas.udobj.belong_application_module";
    String CRM_HOME_PAGE_MODULE = "paas.udobj.crm_home_page_module";
    String START_USING = "paas.udobj.start_using";
    String DEFAULT_LOCK_RULE = "paas.udobj.default_lock_rule";
    String SELECT_ONE_HIDE_FORBID = "paas.udobj.select_one_hide_forbid";
    String FIELD_CIRCLE_FORBID = "paas.udobj.field_circle_forbid";
    String DETAIL_PAGE = "paas.udobj.detail_page";
    String LIST_PAGE = "paas.udobj.list_page";
    String CREATE_PAGE = "paas.udobj.create_page";
    String EDIT_PAGE = "paas.udobj.edit_page";
    String DATA_LIST_PAGE = "paas.udobj.data_list_page";
    String RELATED_LIST = "paas.udobj.related_list_page";
    String DETAIL_INFO = "paas.udobj.detail_info";
    String FORM_COMPONENT = "paas.udobj.form_component";
    String UNIT_PRICE = "paas.udobj.unit_price";
    String QUANTITY = "paas.udobj.quantity";
    String SUBTOTAL = "paas.udobj.subtotal";
    String RETURN_UNIT_PRICE = "paas.udobj.return_unit_price";
    String MODIFY_LOG = "paas.udobj.modify_log";
    String FIELD_DISABLED = "paas.udobj.field_disabled";
    String RELATED_FIELD_DISABLED = "paas.udobj.related_field_disabled";
    String ENABLE = "paas.udobj.enable";
    String DISABLE = "paas.udobj.disable";
    String DELETE = "paas.udobj.action.delete";
    String CREATE = "paas.udobj.create";
    String EDIT = "paas.udobj.edit";
    String QUERY_NAME_FAILED = "paas.udobj.query_name_failed";
    String OBJECT_DESCRIBE_NOT_FOUND = "paas.udobj.object_describe_not_found";
    String RELATED_OBJECT_DESCRIBE_NOT_FOUND = "paas.udobj.related_object_describe_not_found";
    String REPLACE_OBJECT_DESCRIBE_FAILED = "paas.udobj.replace_object_describe_failed";
    String DATA_EMPTY = "paas.udobj.data_empty";
    String MESSAGE_FORMAT_ERROR = "paas.udobj.message_format_error";
    String DATA_CHANGED_BY_SCORE_RULE_LOG = "paas.udobj.data_changed_by_score_rule_log";
    String RECEIPT_MESSAGE_CONTENT = "paas.udobj.receipt_message_content";
    String RECEIPT_MESSAGE_TITLE = "paas.udobj.receipt_message_title";
    String ID_LIST_INVALID = "paas.udobj.id_list_invalid";
    String SYSTEM_ERROR = "paas.udobj.system_error";
    String ADD = "paas.udobj.action.add";
    String MODIFY = "paas.udobj.action.edit";
    String RECOVERY = "paas.udobj.action.recover";
    String CHANGE_OWNER = "paas.udobj.action.change_owner";
    String REMOVE_SALE = "paas.udobj.remove_sale";
    String ASSIGNED = "paas.udobj.action.allocate";
    String TAKE_BACK = "paas.udobj.action.take_back";
    String RECEIVE = "paas.udobj.action.choose";
    String RETURN_BACK = "paas.udobj.action.return";
    String IMPORT = "paas.udobj.action.import";
    String REUSING_ADD = "paas.udobj.reusing_add";
    String RECOVERY_BY_MODIFY = "paas.udobj.recovery_by_modify";
    String RESUING = "paas.udobj.resuing";
    String ASSIST_VISIT = "paas.udobj.assist_visit";
    String CONFIRM = "paas.udobj.action.confirm";
    String REJECT = "paas.udobj.action.reject";
    String RECALL = "paas.udobj.action.recall";
    String MOVE_HSC_CUSTOMER = "paas.udobj.action.move";
    String REMOVE_CUSTOMER_FROM_HS = "paas.udobj.remove_customer_from_hs";
    String ADD_EMPLOYEE = "paas.udobj.add_employee";
    String REMOVE_EMPLOYEE = "paas.udobj.remove_sale";
    String MODIFY_SALE = "paas.udobj.modify_sale";
    String ADD_ADDRESS = "paas.udobj.add_address";
    String MODIFY_ADDRESS = "paas.udobj.modify_address";
    String DELETE_ADDRESS = "paas.udobj.delete_address";
    String ADD_FINANCE = "paas.udobj.add_finance";
    String MODIFY_FINANCE = "paas.udobj.modify_finance";
    String DELETE_FINANCE = "paas.udobj.delete_finance";
    String AUTO_TAKE_BACK = "paas.udobj.auto_take_back";
    String CANCEL_ASSIST_VISIT = "paas.udobj.cancel_assist_visit";
    String EXPORT = "paas.udobj.action.export";
    String HANDLE = "paas.udobj.handle";
    String CONFIRM_DELIVERY = "paas.udobj.action.confirm_delivery";
    String CONFIRM_RECEIVE = "paas.udobj.action.confirm_receipt";
    String RESET = "paas.udobj.reset";
    String WORK_FLOW_START = "paas.udobj.work_flow_start";
    String WORK_FLOW_TAKE_BACK = "paas.udobj.work_flow_take_back";
    String WORK_FLOW_CANCEL = "paas.udobj.work_flow_cancel";
    String WORK_FLOW_COMPLETE = "paas.udobj.work_flow_complete";
    String WORK_FLOW_REJECT = "paas.udobj.work_flow_reject";
    String AUTO_ASSIGNED = "paas.udobj.auto_assigned";
    String CREATE_LAYOUT = "paas.udobj.create_layout";
    String UPDATE_LAYOUT = "paas.udobj.update_layout";
    String DELETE_LAYOUT = "paas.udobj.delete_layout";
    String CREATE_OBJECT = "paas.udobj.create_object";
    String UPDATE_OBJECT = "paas.udobj.update_object";
    String DELETE_OBJECT = "paas.udobj.delete_object";
    String DISABLE_OBJECT = "paas.udobj.disable_object";
    String ENABLE_OBJECT = "paas.udobj.enable_object";
    String CREATE_FIELD = "paas.udobj.create_field";
    String UPDATE_FIELD = "paas.udobj.update_field";
    String DELETE_FIELD = "paas.udobj.delete_field";
    String DISABLE_FIELD = "paas.udobj.disable_field";
    String ENABLE_FIELD = "paas.udobj.enable_field";
    String UPDATE_RECORD_TYPE = "paas.udobj.update_record_type";
    String CREATE_RECORD_TYPE = "paas.udobj.create_record_type";
    String ENABLE_RECORD_TYPE = "paas.udobj.enable_record_type";
    String DIABLE_RECORD_TYPE = "paas.udobj.diable_record_type";
    String DELETE_RECORD_TYPE = "paas.udobj.delete_record_type";
    String CREATE_BUTTION = "paas.udobj.create_buttion";
    String UPDATE_BUTTON = "paas.udobj.update_button";
    String DELETE_BUTTON = "paas.udobj.delete_button";
    String UPDATE_IMPORT = "paas.udobj.update_import";
    String CHANGE_BUTTON_STATUS = "paas.udobj.change_button_status";
    String CHANGE_PARTNER = "paas.udobj.change_partner";
    String CHANGE_PARTNER_OWNER = "paas.udobj.change_partner_owner";
    String DELETE_PARTNER = "paas.udobj.delete_partner";
    String ASSOCIATE = "paas.udobj.action.relate";
    String DIASSOCIATE = "paas.udobj.action.bulk_disrelate";
    String CHANGE_DEAL_STATUS = "paas.udobj.change_deal_status";
    String TRANSFER_PROCESSING_TASK = "paas.udobj.transfer_processing_task";
    String ADD_TEAM_MEMBER_LOG = "paas.udobj.add_team_member_log";
    String ADD_TEAM_MEMBER_LOG_2 = "paas.udobj.add_team_member_log_2";
    String EDIT_TEAM_MEMBER_LOG = "paas.udobj.edit_team_member_log";
    String EDIT_TEAM_MEMBER_LOG_2 = "paas.udobj.edit_team_member_log_2";
    String REMOVE_TEAM_MEMBER_LOG = "paas.udobj.remove_team_member_log";
    String REMOVE_TEAM_MEMBER_LOG_2 = "paas.udobj.remove_team_member_log_2";
    String EDIT_OWNER_LOG_TRANSFER_TO_TEAM_MEMBER = "paas.udobj.edit_owner_log_transfer_to_team_member";
    String EDIT_OWNER_LOG_REMOVE_FROM_TEAM_MEMBER = "paas.udobj.edit_owner_log_remove_from_team_member";
    String PERMISSION_ERROR = "paas.udobj.permission_error";
    String SALE_RECORD = "paas.udobj.sale_record";
    String FOLLOW_UP_DYNAMIC = "paas.udobj.follow_up_dynamic";
    String DATA_TO_INVALID_EMPTY = "paas.udobj.data_to_invalid_empty";
    String DATA_TO_INVALID_FORMAT_ERROR = "paas.udobj.data_to_invalid_format_error";
    String DATA_ID_OR_API_NAME_EMPTY = "paas.udobj.data_id_or_api_name_empty";
    String CHANGE_PARTNER_OWNER_SUCCESS = "paas.udobj.change_partner_owner_success";
    String OUTER_OWNER_EMPTY = "paas.udobj.outer_owner_empty";
    String PARTNER_NOT_EXIST = "paas.udobj.partner_not_exist";
    String WITH_THE_SAME_PARTNER = "paas.udobj.with_the_same_partner";
    String OUTER_OWNER = "paas.udobj.outer_owner";
    String ORIG_OUTER_OWNER = "paas.udobj.orig_outer_owner";
    String NEW_OUTER_OWNER = "paas.udobj.new_outer_owner";
    String CHANGE_PARTNER_SUCCESS = "paas.udobj.change_partner_success";
    String PARNTER_ID_EMPTY = "paas.udobj.parnter_id_empty";
    String DATA_INVALID = "paas.udobj.data_invalid";
    String ORIG_PARTNER = "paas.udobj.orig_partner";
    String NEW_PARTNER = "paas.udobj.new_partner";
    String NOT_EXIST = "paas.udobj.not_exist";
    String ADD_SALE = "paas.udobj.add_sale";
    String CREATE_OR_UPDATE_DUPLICATE_RULE = "paas.udobj.create_or_update_duplicate_rule";
    String UPDATE_DUPLICATE_RULE = "paas.udobj.update_duplicate_rule";

    String SEARCH_DATA_OCCUR_EXCEPTION = "paas.udobj.search_data_occur_exception";
    String OBJECT_NOT_EXIST = "paas.udobj.object_not_exist";
    String OBJECT_CANNOT_BE_FIND = "paas.udobj.object_cannot_be_find";
    String OBJECT_CANNOT_BE_FIND2 = "paas.udobj.object_cannot_be_find2";
    String FIELD_ALREADY_DELETED = "paas.udobj.field_already_deleted";
    String OBJECT_IN_UNEXIST_OR_DELETE = "paas.udobj.object_in_unexist_or_delete";
    String CANNOT_ADD_MASTERDETAIL_FIELD = "paas.udobj.cannot_add_masterdetail_field";
    String CANNOT_ADD_MASTERDETAIL_FIELD_BECAUSE_HAS_FLOW_DEFINITION = "paas.udobj.cannot_add_masterdetail_field_because_has_flow_definition";
    String CANNOT_ADD_REQUIREDL_FIELD = "paas.udobj.cannot_add_requiredl_field";
    String FIELD_CANNOT_HIDDEN_IN_LAYOUT = "paas.udobj.field_cannot_hidden_in_layout";
    String FLOW_LAYOUT_API_NAME_CAN_NOT_BE_EMPTY = "flow_layout_api_name_can_not_be_empty";
    String SLAVE_OBJECTS_BE_PEAK_VALUE = "paas.udobj.slave_objects_be_peak_value";
    String SLAVE_OBJECTS_BE_PEAK_VALUE_V2 = "paas.udobj.slave_objects_be_peak_value_v2";
    String CUSTOM_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.custom_field_beyond_max_limit";
    String OBJECT_REFERENCE_MANY_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.object_reference_many_field_beyond_max_limit";
    /**
     * 人员多选或者部门多选字段数量超出最大限制{0}：{1}
     */
    String EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V2 = "paas.udobj.employe_department_many_field_beyond_max_limit_v2";
    /**
     * 选中项总数超过最大限制，其中{0}
     */
    String EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V3 = "paas.udobj.employe_department_many_field_beyond_max_limit_v3";

    /**
     * {0}:{1}
     */
    String MANY_FIELD_LABEL_APPEND_MAX_COUNT = "paas.udobj.many_field_label_append_max_count";

    /**
     * [{0}]值类型异常
     */
    String VALUE_TYPE_ERROR = "paas.udobj.value_type_error";
    String IMAGE_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.image_field_beyond_max_limit";
    String ENCLOSURE_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.enclosure_field_beyond_max_limit";
    String FORMULA_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.formula_field_beyond_max_limit";
    String RELATION_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.relation_field_beyond_max_limit";
    String INCREASING_CODING_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.increasing_coding_field_beyond_max_limit";
    String QUOTE_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.quote_field_beyond_max_limit";
    String COUNT_FIELD_BEYOND_MAX_LIMIT = "paas.udobj.count_field_beyond_max_limit";
    String HTML_RICH_TEXT_FIELD_LIMIT = "paas.udobj.html_rich_text_field_limit";
    String RICH_TEXT_FIELD_LIMIT = "paas.udobj.rich_text_field_limit";
    String BIG_RICH_TEXT_FIELD_LIMIT = "paas.udobj.big_rich_text_field_limit";
    String WHAT_LIST_FIELD_LIMIT = "paas.udobj.what_list_field_limit";
    String LAYOUT_ALREADY_EXIST = "paas.udobj.layout_already_exist";
    String APINAME_LENGTH_BEYOND_MAX_LIMIT = "paas.udobj.apiname_length_beyond_max_limit";
    String NAME_ALREADY_EXIST = "paas.udobj.name_already_exist";
    String ASSOCIATED_OBJECTS_UNEXIST_OR_DELETED = "paas.udobj.associated_objects_unexist_or_deleted";
    String FILTER_UNEXIST_OR_DELETED = "paas.udobj.filter_condition_quote_field_unexist_or_deleted";
    String FILED_MODIFY_VALIDATE_EXCEPTION = "paas.udobj.filed_modify_validate_exception";
    String FIELD_EXIST_RING_FORMING = "paas.udobj.field_exist_ring_forming";
    String FORMULA_FIELD_EXIST_RING_FORMING = "paas.udobj.formula_field_exist_ring_forming";
    String OBJECT_FIELD_RING_FORMING = "paas.udobj.object_field_ring_forming";
    String QUOTE_GLOBAL_VARIABLE_DISABLED_OR_DELETED = "paas.udobj.quote_global_variable_disabled_or_deleted";
    String QUOTE_OBJECTE_DISABL = "paas.udobj.quote_objecte_disabl";
    String QUOTE_FIELD_ISNOT_ROLE_TYPE = "paas.udobj.quote_field_isnot_role_type";
    String VARIABLE_DISABLED_OR_DELETED = "paas.udobj.quote_form_variable_disabled_or_deleted";
    String EXPRESSION = "pass.udobj.expression";
    String UNSUPPORTED_VARIABLE = "paas.udobj.unsupported_variable";
    String UNSUPPORT_COMPARISON_OPERATOR = "paas.udobj.unsupport_comparison_operator";
    String TARGET_OBJECT_UNEXIST_OR_DISABLED = "paas.udobj.target_object_unexist_or_disabled";
    String SOURCE_OBJECT_UNEXIST_OR_DISABLED = "paas.udobj.source_object_unexist_or_disabled";
    String DELETE_MAPPING_RULE_EXCEPTION = "paas.udobj.delete_mapping_rule_exception";
    String OBJECT_DESC_UNEXIST = "paas.udobj.object_desc_unexist";
    String MAPPING_RULE_DISABLED_OR_DELETED = "paas.udobj.mapping_rule_disabled_or_deleted";
    String LAYOUT_RULE_RING_FORMING = "paas.udobj.layout_rule_ring_forming";
    String FIELD_DELETED_IN_LAYOUT = "paas.udobj.field_deleted_in_layout";
    String FIELD_INVALID_IN_LAYOUT = "paas.udobj.field_invalid_in_layout";
    String NO_PERMISSION_IN_OPERATE = "paas.udobj.no_permission_in_operate";
    String INIT_PERMISSION_FAIL = "paas.udobj.init_permission_fail";
    String GET_FUNCTION_PERMISSION_FAIL = "paas.udobj.get_function_permission_fail";
    String GET_ROLE_LIST_FAIL = "paas.udobj.get_role_list_fail";
    String BATCH_DELETE_PERMISSION_FAIL = "paas.udobj.batch_delete_permission_fail";
    String INIT_PERMISSION_FAIL_REASON = "paas.udobj.init_permission_fail_reason";
    String BATCH_ADD_ROLE_PERMISSION_FAIL_REASON = "paas.udobj.batch_add_role_permission_fail_reason";
    String UPDATE_ROLE_PERMISSION_FAIL_REASON = "paas.udobj.update_role_permission_fail_reason";
    String GET_ROLE_LIST_FAIL_IN_PERMISSION = "paas.udobj.get_role_list_fail_in_permission";
    String DELETE_PERMISSION_FAIL = "paas.udobj.delete_permission_fail";
    String CREATE_ROLE_FAIL_REASON = "paas.udobj.create_role_fail_reason";
    String GET_OUT_ROLE_FAIL_REASON = "paas.udobj.get_out_role_fail_reason";
    String GET_ROLE_INFO_LIST_FAIL = "paas.udobj.get_role_info_list_fail";
    String UPDATE_ROLE_LIST_PERMISSION_FAIL_REASON = "paas.udobj.update_role_list_permission_fail_reason";
    String UPDATE_CUSTOM_BUTTON_FAIL = "paas.udobj.update_custom_button_fail";

    String RELATED = "paas.udobj.related";
    String DEFAULT_LAYOUT_NOT_EXIST = "paas.udobj.default_layout_not_exist";
    String DELETE_OBJECT_FAIL = "paas.udobj.delete_object_fail";
    String OPER_FAIL_MAYBE_REASON = "pass.udobj.oper_fail_maybe_reason";
    String VALIDATE_RULE = "pass.udobj.validate_rule";
    String DETAIL_CANNOT_SET_VALIDATE_RULE = "pass.udobj.detail_cannot_set_validate_rule";
    String FIELD_IN_CALCULATE = "paas.udobj.field_in_calculate";
    String IMAGE_EXCEED_COUNT = "paas.udobj.image_exceed_max_count";
    String IMAGE_EXCEED_MIN_COUNT = "paas.udobj.image_exceed_min_count";
    String ATTACH_EXCEED_COUNT = "paas.udobj.attach_exceed_max_count";
    String DUPLICATE_EFFECTIVE = "paas.udobj.duplicate_effective";
    String DUPLICATE_SUCCESS = "paas.udobj.duplicate_success";
    String EDIT_OWNER_LOG_CHANGE_OWNER = "paas.udobj.edit_owner_log_change_owner";
    String EDIT_OWNER_LOG_CHANGE_OWNER_2 = "paas.udobj.edit_owner_log_change_owner_2";
    String EDIT_OWNER_LOG_OWNER_DEPT = "paas.udobj.edit_owner_log_owner_dept";
    String EDIT_OWNER_LOG_TRANSFER_TEAM_MEMBER = "paas.udobj.edit_owner_log_transfer_team_member";
    String EDIT_OWNER_LOG_REMOVE_TEAM_MEMBER = "paas.udobj.edit_owner_log_remove_team_member";
    String UNEXIST_OR_DELETED = "paas.udobj.unexist_or_deleted";
    String ENABLED_NOT_BE_DELETED = "paas.udobj.enabled_not_be_deleted";
    String PARTIAL_NOT_EXIST = "paas.udobj.import_file_partial_not_exist";
    String IMAGE_MAX_SIZE = "paas.udobj.image_max_size";
    String ATTACH_MAX_SIZE = "paas.udobj.attach_max_size";
    String VIEW = "paas.udobj.action.view";
    String CONTACT_ADMIN = "paas.udobj.action.contact_admin";
    String CONTACT_OWNER = "paas.udobj.action.contact_owner";
    String IMPORT_NOT_EXIST = "paas.udobj.import_file_partial_not_exist";
    String IMAGE = "paas.udobj.image";
    String NO_PERMISSION_VIEW = "paas.udobj.no_permission_view";
    String DISABLED = "paas.udobj.is_disabled";
    String STATUS_REMOVED = "paas.udobj.status_removed";
    String INTERFACE_UPGRADE = "paas.udobj.interface_upgrade";
    String DUPLICATED_DATA = "paas.udobj.duplicated_data";
    String SIGN_IN_ERROR = "paas.udobj.sign_in_error";
    String SIGN_OUT_ERROR = "paas.udobj.sign_out_error";
    String FILED_MODIFY_VALIDATE_TIME_EXCEPTION = "paas.udobj.filed_modify_validate_time_exception";
    String FILED_MODIFY_VALIDATE_ROLE_EXCEPTION = "paas.udobj.filed_modify_validate_role_exception";
    String FILED_MODIFY_VALIDATE_TIME_FUNCTION_EXCEPTION = "paas.udobj.filed_modify_validate_time_function_exception";
    String action_export_file = "paas.udobj.export_file";
    String CHANGE = "paas.udobj.modify";

    String IMPORT_IMAGE_ERROR1 = "paas.udobj.import_water_print_error";
    //唯一性ID
    String DATAID_LABEL = "paas.udobj.data_id";
    String FILED_MODIFY_VALIDATE_DEPARTMENT_EXCEPTION = "paas.udobj.filed_modify_validate_department_exception";
    String FILED_MODIFY_VALIDATE_OWNER_DEPARTMENT = "paas.udobj.filed_modify_validate_owner_department";
    String FILED_MODIFY_VALIDATE_DEPARTMENT = "paas.udobj.filed_modify_validate_department";
    String IMPORT_VERIFY_DUPLICATE = "pass.udobj.import_verify_duplicate";
    String IMPORT_NAME_DUPLICATE = "pass.udobj.import_name_duplicate";
    String FILL_UNQUE_ID = "pass.udobj.fill_unique_id";
    String MEMBER_MANAGER_ROLE = "paas.member.manager.role";

    String FILED_MODIFY_VALIDATE_OWNER_DEPARTMENT_EXCEPTION = "paas.udobj.filed_modify_validate_owner_department_exception";
    String SET_MAIN = "paas.udobj.action.set_main";
    String SET_DEFAULT = "paas.udobj.action.set_default";
    String ACTION_REMOVE = "paas.udobj.action.action_remove";
    String CHANGE_STATUS = "paas.udobj.action.change_status";
    String REMOVE_OBJECT = "paas.udobj.action.remove_object";

    String COMPLETE_FILTER_CRITERIA = "paas.udobj.complete_filter_criteria";

    String PURCHASING_AGENT = "paas.role.purchasing.agent";
    String PURCHASING_AGENT_DESCRIPTION = "paas.role.purchasing.agent.description";
    String CHANNEL_STAFF = "paas.role.channel.staff";
    String CHANNEL_STAFF_DESCRIPTION = "paas.role.channel.staff.description";

    String SELECT_FIELD_FIRST = "paas.udobj.select_field_first";
    String FIELD_RELATION = "paas.udobj.field_relation";
    String FIELD_LIMITED = "paas.udobj.field_limited";
    String DETAILED_PATH = "paas.udobj.detailed_path";
    String FUNCTION_PRIVILEGE_NOT_HAVE_EXPORT_PRIVILEGE = "function.privilege.not.have.export.privilege";
    String PRIVIELGE_USE_VERSION = "privielge.use.version";
    String OBJECT_FUNCTION_PERMISSIONS = "paas.udobj.object_function_permissions";
    String API_UPGRADE = "paas.udobj.api_upgrade";
    String VALIDATE_RULE_VALIDATE_EXCEPTION = "paas.udobj.validate_rule_validate_exception";
    String action_change_states = "paas.udobj.change_states";
    String CONFIGURE_SBUPRODUCT_BUTTON = "product.configure.sbuproduct.button";
    String CONFIGURE_COLLOCATION_BUTTON = "product.configure.collocation.button";
    String CONFIGURE_GROUP_BUTTON = "bom.configure.group.button";
    String CONFIGURE_SYNC_PRODUCT_STRUCTURE_BUTTON = "bom.configure.sync_product_structure.button";
    String CONFIGURE_SYNC_STRUCTURE_TO_OTHER_BUTTON = "bom.configure.sync_structure_to_other.button";
    String CONFIGURE_SET_GROUP_BUTTON = "bom.configure.set_group.button";
    String CONFIGURE_DEL_GROUP_BUTTON = "bom.configure.del_group.button";
    String TEAM_MEMBER_ROLE_IS_EMPTY = "paas.udobj.team_member_role_is_empty";

    String VALID_UNIQUENESS_MESSAGE_DB = "paas.udobj.valid_uniqueness_message_db";
    String VALID_UNIQUENESS_MESSAGE_DUPLICATED = "paas.udobj.valid_uniqueness_message_duplicated";
    String VALID_UNIQUENESS_MESSAGE_NOT_FOUND = "paas.udobj.valid_uniqueness_message_not_found";
    String UNIQUENESS_CONFIGURATION_ERROR = "paas.udobj.uniqueness_configuration_error";
    String UNIQUENESS_FIELD_NOT_EXIST = "paas.udobj.uniqueness_field_not_exist";

    String VIEW_RELATION_OBJECT = "paas.udobj.view_relation_object";
    String OF = "paas.udobj.of";
    String SALES_TEAMMATE = "paas.udobj.sales_teammate";
    String AFTER_SALES_PERSONNEL = "paas.udobj.after_sales_personnel";
    String RELATED_MARK = "paas.udobj.related_mark";
    String IMPORT_SUCCESS_REDIS_FAIL = "paas.udobj.import_redis";
    String IMPORT_MARK_EMPTY = "paas.udobj.import_mark_empty";
    String IMPORT_MASTER_NOT_EXIST = "paas.udobj.import_master_not_exist";
    String IMPORT_NO_ASSOCIATE_FIELD = "paas.udobj.import_no_associate_field";
    String ASSOCIATE_DUPLICATE = "paas.udobj.associate_duplicate";
    String ASSOCIATE_OBJECT_ID = "paas.udobj.associate_obj_id";

    String NORMAL_TEAM_MEMBER_READONLY = "sfa.CustomerBusiness.159.2";
    String NORMAL_TEAM_MEMBER_READWRITE = "sfa.CustomerBusiness.159.3";
    String FOLLOWER_TEAM_MEMBER_READONLY = "sfa.CustomerBusiness.259.1";
    String FOLLOWER_TEAM_MEMBER_READWRITE = "sfa.CustomerBusiness.260.1";
    String AFTERSALES_TEAM_MEMBER_READONLY = "sfa.CustomerBusiness.159.4";
    String AFTERSALES_TEAM_MEMBER_READWRITE = "sfa.CustomerBusiness.258.1";

    String TEAM_MEMBER_NOT_EXIST = "paas.udobj.team_memeber_not_exist";
    String TEAM_MEMBER_CANCELED = "paas.udobj.team_memeber_cancled";
    String FIELD_VALUE_REPEAT = "paas.udobj.field_value_repeat";
    String IMPORT_DATE_TIME_FORMAT_ERROR = "paas.udobj.date_time_error1";

    String TEMPLATE_ID_NOT_EXIST = "paas.udobj.template_id_not_exist";
    String DATA_OWNER_NOT_EXIT = "paas.udobj.data_owner_not_exit";
    String UNIQUENESS_RULE_IS_IN_EFFECT = "paas.udobj.uniqueness_rule_is_in_effect";
    String NOT_HAVE_FIELD_PERMISSION_CANNOT_IMPORT = "paas.udobj.not_have_field_permission_cannot_import";
    String UNIQUENESS_RULE_MISSING_COLUMN = "paas.udobj.uniqueness_rule_missing_column";
    String NOT_SUPPORTED_INSERT_IMPORT_BY_UNIQUENESS = "paas.udobj.not_supported_insert_import_by_uniqueness";
    String UNIQUENESS_RULE_FIELDS_NOT_FILLED = "paas.udobj.uniqueness_rule_fields_not_filled";
    String CUSTOM_FUNCTION_MANAGER = "paas.udobj.custom_function_manager";
    String HANDLE_CUSTOM_FUNCTION = "paas.udobj.handle_custom_function";
    String CREATE_FUNCTION = "paas.udobj.create_function";
    String UPDATE_FUNCTION = "paas.udobj.update_function";
    String DELETE_FUNCTION = "paas.udobj.delete_function";
    String DISABLE_FUNCTION = "paas.udobj.disable_function";
    String ENABLE_FUNCTION = "paas.udobj.enable_function";
    String TEAM_MEMBER_LESS_50 = "pass.udobj.team-member_more_than_fifty";
    String VALIDATE_FUNCTION = "paas.udobj.validate_function";
    String FUNCTION_BINDING_ERROR = "paas.udobj.function.binding.error";
    String FUNCTION_BINDING_NOT_EXIST = "paas.udobj.function.binding.not.exist";
    String FIND_FUNCTION_API_NAME_ERROR = "paas.udobj.function.find.api_name.not.exist";

    String NO_UNIQUE_RULES_AVAILABLE = "pass.udobj.no_unique_rules_available";
    String UNIQUENESS_RULE_SERVICE_EXCEPTION = "pass.udobj.Uniqueness_rule_service_exception";

    String UNIQUENESS_RULE_FIELDS_NOT_EMPTY = "paas.udobj.uniqueness_rule_fields_not_empty";
    String UNIQUENESS_RULE_FIELDS_NOT_ALL_EMPTY = "paas.udobj.uniqueness_rule_fields_not_all_empty";

    String TENANT_SCENE_COUNT = "pass.udobj.tenant_scene_count";
    String TENANT_SCENE_COUNT_VALIDATE = "pass.udobj.tenant_scene_count_validate";
    String SCENE_ID_NOT_EMPTY = "pass.udobj.scene_id_not_empty";
    String SCENE_API_NAME_NOT_EMPTY = "pass.udobj.scene_api_name_not_empty";
    String SCENE_NOT_EXIST_OR_DELETE = "pass.udobj.scene_not_exist_or_delete";
    String SCENE_BINDING_OBJECT_IS_INCONSISTENT = "pass.udobj.scene_binding_object_is_inconsistent";
    String SCENE_RANGE_NOT_EMPTY = "pass.udobj.scene_range_not_empty";
    String SCENE_LABEL_DUPLICATE = "pass.udobj.scene_label_duplicate";
    String INCONSISTENT_SCENE_TYPES = "pass.udobj.inconsistent_scene_types";
    String SCENE_API_NAME_REPEAT = "pass.udobj.scene_api_name_repeat";
    String FIELD_IS_DISABLED_DELETED = "pass.udobj.field_is_disabled_deleted";
    String FIELD_UNSUPPORT_FILTER = "pass.udobj.field_unsupport_filter";
    String OPTION_FOR_FIELD_REMOVED = "pass.udobj.option_for_field_removed";
    String SCENE_NAME_NOT_EMPTY = "paas.udobj.scene_name_not_empty";
    String RECORD_TYPE_IS_DELETE = "paas.udobj.record_type_is_delete";
    String OWN_OBJECT_NOT_EMPTY = "paas.udobj.own_object_not_empty";
    String HEADER_FIELD_NOT_LESS_TWO = "paas.udobj.header_field_not_less_two";
    String SCENE_NAME_EXCEED = "paas.udobj.scene_name_exceed";
    String DESCRIPTION_LIMIT = "paas.udobj.description_limit";
    String NOT_FIND_FUNCTION = "pass.udobj.not_find_function";
    String UPLOAD_EXCEL_FAIL = "pass.udobj,upload_excel_fail";
    String FILE_IS_TOO_LARGE = "pass.udobj.file_is_too_large";
    String EXPORT_FILE_TOO_LARGE = "pass.udobj.export_file_too_large";
    String OBJECT_REFERENCE_FUNCTION_VALIDATE = "paas.udobj.object_reference_function_validate";
    String CHANGE_REASON = "paas.udobj.change_reason";
    String DA_VINCI = "paas.udobj.da_vinci";
    String EXTERNAL_PERSON = "paas.udobj.external_person";

    String DATA_EXPIRED = "paas.metadata.data.expired";
    String INCREASE = "paas.metadata.data.increase";
    String CONVERT_NORMAL_MEMBER = "paas.metadata.convert_normal_member";
    String REMOVED_RELATED_TEAM = "paas.metadata.removed_related_team";

    String CREATE_TEMPORARY_RIGHTS = "paas.metadata.create_temporary_rights";
    String UPDATE_TEMPORARY_RIGHTS = "paas.metadata.update_temporary_rights";
    String ENABLE_TEMPORARY_RIGHTS = "paas.metadata.enable_temporary_rights";
    String DISABLE_TEMPORARY_RIGHTS = "paas.metadata.disable_temporary_rights";
    String DELETE_TEMPORARY_RIGHTS = "paas.metadata.delete_temporary_rights";
    String FUNCTION_EXECUTION_ERROR = "paas.udobj.function_error";
    String FUNCTION_FORBIDDEN_WARN = "paas.udobj.function_forbidden";

    String QUOTE_FILED_MODIFY_VALIDATE_EXCEPTION = "paas.udobj.quote.filed_modify_validate_exception";
    String QUOTE_FILED_MODIFY_VALIDATE_EXCEPTION_NEW = "paas.udobj.quote.filed_modify_validate_exception_new";
    String TEMPORARY_PRIVILEGE_MANAGER = "paas.udobj.temporary_privilege_manager";
    String DATA_SHARING = "paas.udobj.data_sharing";
    String DATA_PERMISSION = "paas.udobj.data_permission";
    String DEFAULT_MOBILE_LIST_LAYOUT_DISPLAY_NAME = "wechat_union.core.wechat_fan_obj_constant.default_list_page_of_mobile";
    String DEFAULT_LIST_LAYOUT = "paas.udobj.default_list_layout";
    String UI_EVENT_EXCEED = "paas.udobj.ui.quota.exceed";
    String UI_EVENT_SAVE_FAIL = "paas.udobj.ui.save.fail";
    String UI_EVENT_DELETE_FAIL = "paas.udobj.ui.delete.fail";
    String UI_EVENT_UPDATE_FAIL = "paas.udobj.ui.update.fail";
    String UI_EVENT_FUNCTION_DELETE = "paas.udobj.ui.function.delete";
    String LAYOUT_RULE_UNIQUE_VALIDATE = "paas.udobj.layout.rule.unique.validate";

    String UNIQ_SAVE_DATA_FAIL = "uniq.save.data.fail";
    String DUPLICATED_SEARCH_SAVE_DATA_FAIL = "paas.udobj.duplicated.search.save.data.fail";

    String BULK_BUTTON_CRM_MESSAGE_TITLE = "paas.udobj.bulk.button.crm.message.title";
    String BULK_BUTTON_CRM_MESSAGE_CONTENT = "paas.udobj.bulk.button.crm.message.content";
    String TIME_OUT = "paas.udobj.network.timeout";

    String DOWNSTREAM_ENTERPRISES_DO_NOT_SUPPORT = "paas.udobj.downstream.enterprises.do.not.support";
    String QUOTE_IMAGE_NOT_SUPPORT_LOW_VERSION = "paas.udobj.quote.image.not.support.low.version";
    String NOT_SATISFY_BUTTON_CONDITION = "paas.udobj.not_satisfy_button_condition";
    String POST_ACTION_FAILED = "paas.udobj.post_action_failed";

    String EDIT_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION = "paas.udobj.edit_approval_flow_can_not_operation_data_should_unlock_operation";
    String CUSTOM_BUTTON_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION = "paas.udobj.custom_button_approval_flow_can_not_operation_data_should_unlock_operation";
    String STAGE_CHANGE_APPROVAL_FLOW_CAN_NOT_OPERATION_DATA_SHOULD_UNLOCK_OPERATION = "paas.udobj.stage_change_approval_flow_can_not_operation_data_should_unlock_operation";

    String MASTER_DATA_INVALID_OR_DELETED = "paas.udobj.master_data_invalid_or_deleted";
    String CANNOT_OPERATE_BECAUSE_OF_MASTER_LOCKED = "paas.udobj.cannot_operate_because_of_master_locked";
    String CANNOT_OPERATE_BECAUSE_OF_MASTER_IN_CHANGE = "paas.udobj.cannot_operate_because_of_master_in_change";
    String CANNOT_IMPORT_BECAUSE_OF_MASTER_LOCKED = "paas.udobj.cannot_import_because_of_master_locked";
    String CANNOT_RECOVER_DETAIL_CREATE_WITH_MASTER = "paas.udobj.cannot_recover_detail_create_with_master";
    String DETAIL_HINT_FOR_LOW_MOBILE_WHEN_TRIGGER_MASTER_APPROVAL = "paas.udobj.detail_hint_for_low_mobile_when_trigger_master_approval";
    String RELATED_TEAM_DATA_PERMISSION_LOG = "paas.udobj.related_team_data_permission_log";
    String RELATED_TEAM_DATA_PERMISSION_UPDATE_LOG_DETAIL = "paas.udobj.related_team_data_permission_update_log_detail";
    String RELATED_TEAM_DATA_PERMISSION_REMOVE_LOG_DETAIL = "paas.udobj.related_team_data_permission_remove_log_detail";
    String UPDATE_RELATED_TEAM_DATA_PERMISSION = "paas.udobj.update_related_team_data_permission";
    String AUTO_NUMBER_VALIDATE_MESSAGE = "paas.udobj.auto_number_validate_message";

    String CONNECTED_ENTERPRISE = "paas.udobj.connected_enterprise";

    String NO_TITLE = "paas.udobj.no_title";

    String BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_LOCK = "paas.udobj.button.filter.text.object.lock.status.is.lock";
    String BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK = "paas.udobj.button.filter.text.object.lock.status.is.unlock";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_IS_UNASSIGNED = "paas.udobj.button.filter.text.biz.status.is.unassigned";
    String BUTTON_FILTER_TEXT_LIFE_STATUS_IS_NORMAL = "paas.udobj.button.filter.text.life.status.is.normal";
    String BUTTON_FILTER_TEXT_LIFE_DISTRIBUTION_STATUS_IS_NORMAL = "paas.udobj.button.filter.text.life.distribution.status.is.normal";
    String BUTTON_FILTER_TEXT_HIGH_SEAS_IS_NOT_EMPTY = "paas.udobj.button.filter.text.high.seas.is.not.empty";
    String BUTTON_FILTER_TEXT_HIGH_SEAS_MEMBER = "paas.udobj.button.filter.text.high.seas.member";
    String BUTTON_FILTER_TEXT_HIGH_SEAS_ADMINISTRATOR = "paas.udobj.button.filter.text.high.seas.administrator";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_IS_ALLOCATED = "paas.udobj.button.filter.text.biz.status.is.allocated";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_IS_UNASSIGNED_OR_ALLOCATED = "paas.udobj.button.filter.text.biz.status.is.unassigned.or.allocated";
    String BUTTON_FILTER_TEXT_LEADS_POOL_IS_NOT_EMPTY = "paas.udobj.button.filter.text.leads.pool.is.not.empty";
    String BUTTON_FILTER_TEXT_LEADS_POOL_MEMBER = "paas.udobj.button.filter.text.leads.pool.member";
    String BUTTON_FILTER_TEXT_LEADS_POOL_ADMINISTRATOR = "paas.udobj.button.filter.text.leads.pool.administrator";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_TRANSFORMED = "paas.udobj.button.filter.text.biz.status.not.transformed";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_UNASSIGNED = "paas.udobj.button.filter.text.biz.status.not.unassigned";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_UN_PROCESSED = "paas.udobj.button.filter.text.biz.status.un_processed";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_PROCESSED = "paas.udobj.button.filter.text.biz.status.processed";
    String BUTTON_FILTER_TEXT_BIZ_STATUS_CLOSED = "paas.udobj.button.filter.text.biz.status.closed";

    String CANNOT_MAPPING_SLAVE_OBJECT = "paas.udobj.cannot_mapping_slave_object";
    String HEAD_INFO = "paas.udobj.head_info";
    String SUMMARY_INFO = "paas.udobj.summary_info";
    String SUMMARY_CARD = "paas.udobj.summary_card";
    String SALE_LOG = "paas.udobj.sale_log";
    String CONTAINER_TABS = "paas.udobj.contain_tabs";
    String FRAME_COMPONENT = "paas.udobj.frame_component";
    String PRODUCT_ATTRIBUTE = "paas.udobj.product_attribute";
    String ACCOUNT_OPERATION_MAP = "paas.udobj.account_operation_map";
    String MARKETING_EVENT_PATH = "paas.udobj.marketing_event_path";

    String TRANSLATE_FAIL = "paas.udobj.translate_fail";

    String APPROVAL_COMPONENT = "paas.udobj.approval_component";
    String STAGE_COMPONENT = "paas.udobj.stage_component";
    String BPM_COMPONENT = "paas.udobj.bpm_component";

    String NAME_COMPONENT = "paas.udobj.name_component";

    String CANNOT_EDIT_OBJECT_QUICKLY = "paas.udobj.cannot_edit_object_quickly";
    String CANNOT_EDIT_FIELD_QUICKLY = "paas.udobj.cannot_edit_field_quickly";
    String CANNOT_EDIT_LOCKED_DATA = "paas.udobj.cannot_edit_locked_data";
    String HAS_NO_FIELD_WRITE_PERMISSION = "paas.udobj.has_no_field_write_permission";
    String HAS_NO_FIELD_WRITE_PERMISSION_WITH_FIELD_INFO = "paas.udobj.has_no_field_write_permission_with_field_info";
    String HAS_NO_FIELD_WRITE_PERMISSION_IN_LAYOUT = "paas.udobj.has_no_field_write_permission_in_layout";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_STAGE_INSTANCE = "paas.udobj.cannot_edit_field_quickly_because_of_stage_instance";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_UI_EVENT = "paas.udobj.cannot_edit_field_quickly_because_of_ui_event";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_UI_EVENT_REFERENCE = "paas.udobj.cannot_edit_field_quickly_because_of_ui_event_reference";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_LAYOUT_RULE = "paas.udobj.cannot_edit_field_quickly_because_of_layout_rule";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_LAYOUT_RULE_REFERENCE = "paas.udobj.cannot_edit_field_quickly_because_of_layout_rule_reference";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_CALCULATE_RELATION = "paas.udobj.cannot_edit_field_quickly_because_of_calculate_relation";
    String CANNOT_EDIT_FIELD_QUICKLY_BECAUSE_OF_MASK_FIELD_REFERENCE = "paas.udobj.cannot_edit_field_quickly_because_of_mask_field_reference";

    String GET_DOWNSTREAM_TENANT_ERROR = "paaas.udobj.get_downstream_tenant_error";

    String VALIDATE_RECORD_TYPE_BY_FUNCTION_PRIVILEGE = "paaas.udobj.validate_record_type_by_function_privilege";
    String VALIDATE_RECORD_TYPE_BY_FUNCTION_PRIVILEGE_TRANSFER_ADD = "paaas.udobj.validate_record_type_by_function_privilege_transfer_add";
    String VALIDATE_RECORD_TYPE_BY_FUNC_PRIVILEGE = "paaas.udobj.validate_record_type_by_func_privilege";

    String CANNOT_ADD_IF_NO_OWNER = "paas.udobj.cannot_add_if_no_owner";


    String MOBILE_NEED_IMAGE_CODE = "paas.udobj.mobile.need_image_code";
    String MOBILE_IMAGE_CODE_ERROR = "paas.udobj.mobile.image_code_error";
    String MOBILE_IP_LIMITED = "paas.udobj.mobile.ip_limited";
    String MOBILE_SMS_SEND_ERROR = "paas.udobj.mobile.sms_send_error";
    String MOBILE_SMS_TOO_OFTEN = "paas.udobj.mobile.send_too_often";
    String MOBILE_AREACODE_NOT_SUPPORT = "paas.udobj.mobile.areacode_too_often";
    String MOBILE_BUSSINESS_NOT_SUPPORT = "paas.udobj.mobile.bussiness_not_support";
    String MOBILE_SMS_CODE_ERROR = "paas.udobj.mobile.sms_code_error";
    String MOBILE_VERIFY_CODE_EXCEED_LIMIT = "paas.udobj.mobile.verify_code_exceed_limit";

    String LEAST_ONE_BUTTON = "paas.udobj.least_one_button";
    String CANNOT_UPDATE_MASK_FIELD = "paas.udobj.cannot_update_mask_field";
    String PRARM_ALL_IS_MASK_FIELD = "paas.udobj.prarm_all_is_mask_field";

    String PAY_REMIND_CONTENT = "paas.udobj.pay_remind_content";
    String NEW_PAY_REMIND_CONTENT = "paas.udobj.new_pay_remind_content";
    String CHART_COMPONENT = "paas.udobj.chart_component";
    String ACCOUNT_HIERARCHY_COMPONENT = "paas.udobj.account_hierarchy_component";
    String CONTACT_RELATION_COMPONENT = "paas.udobj.contact_relation_component";

    String IMPORT_RELATED_MARK = "paas.udobj.union_related_mark";
    String CHART_GROUP = "paas.udobj.chart_group.%s_%s";
    String UNNAME = "pass.udobj.uname_folder";

    String FUNC_INCREMENT_NUMBER_FAIL = "paas.udobj.func_increment_number_fail";
    String FUNC_INCREMENT_NUMBER_CONDITION_NOT_EXIST = "paas.udobj.func_increment_number_condition_not_exist";
    String FUNC_INCREMENT_NUMBER_COUNTER_IS_EMPTY = "paas.udobj.func_increment_number_counter_is_empty";
    String FUNC_INCREMENT_NUMBER_VALUE_TOO_LONG = "paas.udobj.func_increment_number_value_too_long";
    String WATERMARK_IMAGE_CANNOT_UPDATED_TO_NON_WATERMARK_IMAGE = "paas.udobj.watermark_image_cannot_updated_to_non_watermark_image";
    String SLAVE_OBJECT_NOT_UPDATE_DATA_OWN_DEPARTMENT = "paas.udobj.can_not_update_data_own_department";
    String SLAVE_OBJECT_NOT_UPDATE_FIELD = "paas.udobj.can_not_update_field";

    String DO_NOT_SUBMIT_REPEATEDLY = "paas.udobj.do_not_submit_repeatedly";
    String TRANSFER_PARTNER = "pass.udobj.transfer_partner";

    String GEO_LOCATION_FIELD_COUNT = "paas.udobj.geo_location_field_count";
    String TRANSFER_ADD = "pass.udobj.transfer_add";
    String PRICE_TOOL = "pass.udobj.price_tool";

    String action_BulkPause = "paas.udobj.action.personnel.BulkPause";
    String action_BulkAllow = "paas.udobj.action.personnel.BulkAllow";
    String action_BulkResetPassword = "paas.udobj.action.personnel.BulkResetPassword";
    String action_BulkActive = "paas.udobj.action.personnel.BulkActive";
    String action_BulkModifyLeader = "paas.udobj.action.personnel.BulkModifyLeader";
    String action_BulkStop = "paas.udobj.action.personnel.BulkStop";
    String action_BulkResume = "paas.udobj.action.personnel.BulkResume";
    String action_BulkResetMainDepartment = "paas.udobj.action.personnel.BulkResetMainDepartment";
    String action_BulkResetViceDepartment = "paas.udobj.action.personnel.BulkResetViceDepartment";
    String action_BulkAddViceDepartment = "paas.udobj.action.personnel.BulkAddViceDepartment";
    String action_BulkDeleteViceDepartment = "paas.udobj.action.personnel.BulkDeleteViceDepartment";

    String action_BulkRemoveBind = "paas.udobj.action.deviceBind.BulkRemoveBind";
    String action_BulkAddDeviceWhiteList = "paas.udobj.action.deviceBind.BulkAddDeviceWhiteList";
    String action_BulkRemoveDeviceBind = "paas.udobj.action.employeeList.BulkRemoveDeviceBind";

    String action_BulkModifyManager = "paas.udobj.action.department.BulkModifyManager";
    String action_BulkModifyAssistant = "paas.udobj.action.department.BulkModifyAssistant";

    //字段依赖成环
    String FIELD_CASCADE_CIRCLE = "paas.udobj.field_cascade_circle";

    // 725 下游crm提醒优化
    String ASSIGN_725 = "paas.udobj.assign.725";
    String UNASSIGN_725 = "paas.udobj.unassign.725";
    String OUTOWNER_CHANGE = "paas.udobj.outOwnerChange";
    String FOLLOW_CUSTOME = "paas.udobj.follow.custom";

    //730产品关联属性
    String action_AssociateAttribute = "paas.udobj.action.personnel.AssociateAttribute";
    String action_DisAssociateAttribute = "paas.udobj.action.personnel.DisAssociateAttribute";
    String action_AssociateNonstandardAttribute = "paas.udobj.action.personnel.AssociateNonstandardAttribute";
    String action_DisAssociateNonstandardAttribute = "paas.udobj.action.personnel.DisAssociateNonstandardAttribute";
    String action_Enable = "paas.udobj.action.personnel.Enable";
    String action_DisEnable = "paas.udobj.action.personnel.DisEnable";

    String DUPLICATE_DATA_IN_IMPORTED_DATA = "paas.udobj.duplicate_data_in_imported_data";

    String MULTI_DUPLICATE_DATA_IN_IMPORTED_DATA = "paas.udobj.multi.duplicate_data_in_imported_data";

    String MULTI_DUPLICATE_DATA_IN_IMPORTED_DATA_SYSTEM = "paas.udobj.multi.duplicate_data_in_imported_data_system";
    String DUPLICATE_DATA_IN_SYSTEM = "paas.udobj.duplicate_data_in_system";

    String CLEAN_EFFECTIVE = "paas.udobj.clean_effective";
    String CLEAN_SUCCESS = "paas.udobj.clean_success";

    //735
    String QIXIN = "paas.udobj.qixin";
    String FIELD_UNIQUE_VALID = "paas.udobj.field_unique_valid";
    // String CASCADE_PARENT_FIELD = "paas.udobj.cascade_parent_field";
    String CUSTOM_BUTTON_LIMIT = "paas.udobj.custom_button_limit";
    String VALIDATE_FUNC_ERROR_MESSAGE = "paas.udobj.validate_func_error_message";

    // 快速维护产品到价目表中
    String FAST_PRODUCT_ASSOCIATE_PRICE_BOOK = "paas.udobj.action.personnel.FastProductAssociatePriceBook";
    // 批量编辑价目表明细
    String EDIT_PRICE_BOOK_PRODUCT = "paas.udobj.action.personnel.EditPriceBookProduct";

    //740
    String MASTER_DETAIL_CURRENCY_NOT_EQUAL = "paas.udobj.master_detail_currency_not_equal";
    String CURRENCY_CANNOT_BE_EMPTY = "paas.udobj.currency_cannot_be_empty";
    String CURRENCY_OPTION_NOT_EXIST = "paas.udobj.currency_option_not_exist";

    String MULTI_CURRENCY_PARAM_ERROR = "paas.udobj.multi_currency_param_error";
    String MULTI_CURRENCY_DATA_ERROR = "paas.udobj.multi_currency_data_error";
    String MULTI_CURRENCY_OPEN_EXCEPTION = "paas.udobj.multi_currency_open_exception";
    String CANNOT_ADD_DUPLICATE_CURRENCY = "paas.udobj.cannot_add_duplicate_currency";
    String FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR = "paas.udobj.functional_currency_cannot_be_operator";
    String FUNCTIONAL_CURRENCY_NOT_EXIST = "paas.udobj.functional_currency_not_exist";

    //745
    String FOLLOW_UP_DYNAMIC_TARGET = "paas.udobj.follow_up_dynamic_target";
    String FOLLOW_UP_QUERY_EXCEPTION = "paas.udobj.follow_up_query_exception";
    String VALIDATE_PARAMS_IDEMPOTENT_FAILED = "paas.udobj.validate_params_idempotent_failed";

    //745
    String FILE_ALREADY_EXPIRE = "paas.udobj.file_already_expire";
    String IMAGE_ALREADY_EXPIRE = "paas.udobj.image_already_expire";
    String action_choose_main_data = "paas.udobj.action.choose_main_data";
    String action_allocate_main_data = "paas.udobj.action.allocate_main_data";
    String action_process_leads = "paas.udobj.action.process_leads";

    //745 阶段推进器添加功能权限

    String STAGE_MOVETO = "paas.udobj.action.stage.moveto";
    String CHANGE_STAGE_CANDIDATEIDS = "paas.udobj.action.change.stage.candidateIds";
    String STAGE_BACKTO = "paas.udobj.action.stage.backto";
    String STAGE_REACTIVATION = "paas.udobj.action.stage.reactivation";

    // 745 取消入账
    String CANCEL_ENTRY = "paas.udobj.action.cancel_entry";
    String DATA_OWN_ORGANIZATION_LABEL = "I18nCRMSystemObj.field.data_own_organization.label";

    String OUTER_DUPLICATED_LIMIT = "paas.udobj.outer_duplicated_limit";
    String SERVICE_BUSY_TRY_AGAIN_LATER = "paas.udobj.service_busy_try_again_later";

    String DOWNSTREAM_OBJECT_CANNOT_EDIT_DELETE = "paas.udobj.downstream_object_cannot_edit_delete";

    //750
    String TARGET_RELATED_LIST_NAME_DUPLICATION = "paas.udobj.target_related_list_name_duplication";
    String BUSINESS_LABEL = "paas.udobj.business_label";
    String TOWN_FIELD_DESCRIBE_NULL = "paas.udobj.town_field_describe_null";
    String DUPLICATE_LAYOUT_FIELD = "paas.udobj.duplicate_layout_field";
    String ZHONG_GUANG_CUN_JIE_DAO = "paas.udobj.zhong_guan_cun_jie_dao";
    String ZHI_CHUN_LU_SHE_QU = "paas.udobj.zhi_chun_lu_she_qu";
    String EXCEED_BODY_LIMIT_SIZE = "paas.udobj.exceed_body_limit_size";
    String WHOLE_GROUP = "paas.udobj.whole_group";
    String COMPANY_WIDE = "paas.udobj.company_wide";
    String PACKAGING_FAILED = "paas.udobj.packaging_failed";
    String FILE_PACK_FAILED = "paas.udobj.file_pack_failed";

    //755
    /**
     * {0}不能为空。
     */
    String AREA_NOT_NULL = "paas.udobj.area_not_null";
    String TOWN_NOT_NULL = "paas.udobj.town_not_null";
    String VIEW_LOGISTICS_PREDEFINE = "paas.udobj.action.view_logistics_predefine";
    String CONFIRM_RECEIPT_PREDEFINE = "paas.udobj.action.confirm_receipt_predefine";

    //760
    String view_bpm_instance_log = "paas.udobj.view.bpm.instance.log";
    String Comment = "paas.udobj.action.Journal.Comment";
    String AsyncBulkComment = "paas.udobj.action.Journal.AsyncBulkComment";
    String RICH_TEXT_FIELD_COUNT = "paas.udobj.rich_text_field_count";
    String VALIDATE_RICH_TEXT_FIELD_COUNT = "paas.udobj.validate_rich_text_field_count";
    String VALIDATE_COOPERATIVE_RICH_TEXT_FIELD_COUNT = "paas.udobj.validate_cooperative_rich_text_field_count";

    String OUT_EMPLOYEE_CANNOT_BE_UPDATED_SEPARATELY = "paas.udobj.out_employee_cannot_be_updated_separately";
    String ONLY_ALLOW_PARTNER_EXIST_IMPORT = "paas.udobj.only_allow_partner_exist_import";
    String OUT_EMPLOYEE_IS_NOT_PARTNER_RELATION = "paas.udobj.out_employee_is_not_partner_relation";
    String CAN_NOT_CONTAIN_SCRIPT_TAG = "paas.udobj.can_not_contain_script_tag";
    String BUTTON_PARAM_ERROR = "paas.udobj.button_param_error";

    String CANCEL_DEVICE_PLAN_PUBLISH = "paas.udobj.action.cancel_device_plan_publish";
    String DEVICE_PLAN_PUBLISH = "paas.udobj.action.device_plan_publish";
    String DEVICE_PLAN_DETAIL_CREATE_CASES = "paas.udobj.action.device_plan_detail_create_cases";
    String CANCEL_PREVENTIVE_MAINTENANCE_PUBLISH = "paas.udobj.action.cancel_preventive_maintenance_publish";
    String PREVENTIVE_MAINTENANCE_PUBLISH = "paas.udobj.action.preventive_maintenance_publish";
    String PREVENTIVE_MAINTENANCE_CREATE_CASES = "paas.udobj.action.preventive_maintenance_create_cases";
    String CANCEL_CASES_BPM = "paas.udobj.action.cancel_cases_bpm";
    String CASES_MULTIPLE_CHECKINS = "paas.udobj.action.cases_multiple_checkins";
    String CASES_RELATED_INFO = "paas.udobj.action.cases_related_info";
    String CASES_KNOWLEDGE_RECOMMEND = "paas.udobj.action.cases_knowledge_recommend";
    String RECEIVE_CASES = "paas.udobj.action.receive_cases";
    String VIEW_CASES_ENGINEER_BUSY_IDLE = "paas.udobj.action.view_cases_engineer_busy_idle";
    String UPDATE_CASES_DEVICE_ADDRESS = "paas.udobj.action.update_cases_device_address";
    String COMPLETE_FEE_SETTLEMENT_BILL = "paas.udobj.action.complete_fee_settlement_bill";
    String CONFIRM_RECEIVE_MATERIAL_APPLY = "paas.udobj.action.confirm_receive_material_apply";
    String REJECT_RECEIVE_MATERIAL_APPLY = "paas.udobj.action.reject_receive_material_apply";
    String SUBMIT_RECEIVE_MATERIAL_APPLY = "paas.udobj.action.submit_receive_material_apply";
    String CONFIRM_REFUND_MATERIAL_APPLY = "paas.udobj.action.confirm_refund_material_apply";
    String REJECT_REFUND_MATERIAL_APPLY = "paas.udobj.action.reject_refund_material_apply";
    String SUBMIT_REFUND_MATERIAL_APPLY = "paas.udobj.action.submit_refund_material_apply";
    String PREVIEW_KNOWLEDGE = "paas.udobj.action.preview_knowledge";
    String UPDATE_KNOWLEDGE_CATEGORY = "paas.udobj.action.update_knowledge_category";
    String OBJECT_BY_COMBINING = "paas.udobj.object_by_combining";
    String CASES_CALL_OUT = "paas.udobj.cases_call_out";
    String APPRAISE_SKIP_RETURN_VISIT = "paas.udobj.appraise_skip_return_visit";
    String REASSIGN_CASES = "paas.udobj.reassign_cases";
    String REASSIGN_CASES_ENGINEER = "paas.udobj.reassign_cases_engineer";
    String REASSIGN_CASES_SERVICEGROUP = "paas.udobj.reassign_cases_servicegroup";
    String REASSIGN_CASES_SERVICEPROVIDER = "paas.udobj.reassign_cases_serviceprovider";
    String CONSULT_QUESTION_RECORD_INEFFECTIVE = "paas.udobj.consult_question_record_ineffective";
    String CONSULT_QUESTION_RECORD_UNDETERMINED = "paas.udobj.consult_question_record_undetermined";
    String CONSULT_QUESTION_RECORD_ADD_KNOWLEDGE = "paas.udobj.consult_question_record_add_knowledge";
    String START_MEETING = "paas.udobj.start_meeting";
    String SERVICE_KNOWLEDGE_SHARE = "paas.udobj.service_knowledge_share";
    String ENTERPRISE_ORDER_COLLECTION = "paas.udobj.enterprise_order_collection";
    String ENTERPRISE_ORDER_REFUND = "paas.udobj.enterprise_order_refund";
    String SYNC_ENTERPRISE_ORDER_COLLECTION_RESULT = "paas.udobj.sync_enterprise_order_collection_result";
    String VIEW_FAULT_TREE = "paas.udobj.view_fault_tree";
    String VOICE_TO_TEXT = "paas.udobj.voice_to_text";
    String WRITE_EXPRESS_BILL = "paas.udobj.write_express_bill";
    String CANCEL_REQUEST = "paas.udobj.cancel_request";
    String SERVICE_REQUEST_VIEW_LOGISTICS = "paas.udobj.service_request_view_logistics";
    String SIGN_FOR_EXPRESS = "paas.udobj.sign_for_express";
    String VIEW_CHAT_RECORD = "paas.udobj.view_chat_record";
    String MAIL_REPLY = "paas.udobj.mail_reply";
    String MAIL_REPLY_ALL = "paas.udobj.mail_reply_all";
    String MAIL_FORWARD = "paas.udobj.mail_forward";
    String MAIL_FORWARD_TO_MESSAGE = "paas.udobj.mail_forward_to_message";
    String MAIL_FORWARD_TO_ACTIVE_RECORD = "paas.udobj.mail_forward_to_active_record";
    String TRANSFER_SESSION = "paas.udobj.transfer_session";
    String END_SESSION = "paas.udobj.end_session";
    String VIEW_LEAVE_MESSAGE = "paas.udobj.view_leave_message";
    String MAKE_UP_CHECKINS = "paas.udobj.make_up_checkins";
    String QUERY_USER = "paas.udobj.query_user";
    String ADD_TO_KNOWLEDGE = "paas.udobj.add_to_knowledge";
    String QAP_SAVE_AND_PUBLISH = "paas.udobj.qap_save_and_publish";
    String QAP_PUBLISH = "paas.udobj.qap_publish";
    String QAP_AI_GENERATED = "paas.udobj.qap_ai_generated";

    String RULE_DISABLE = "paas.udobj.rule_disable";
    String RULE_ENABLE = "paas.udobj.rule_enable";
    String ADJUST_PRIORITY = "paas.udobj.adjust_priority";
    String EXECUTION_RULE = "paas.udobj.execution_rule";
    String KNOWLEDGE_CANCEL_PUBLISH = "paas.udobj.knowledge_cancel_publish";
    String KNOWLEDGE_PUBLISH = "paas.udobj.knowledge_publish";
    String KNOWLEDGE_SAVE_AND_PUBLISH = "paas.udobj.knowledge_save_and_publish";
    String ADD_ENGINEER = "paas.udobj.add_engineer";
    String REASSIGN_ASSOCIATE_ENGINEER_CASES = "paas.udobj.reassign_associate_engineer_cases";
    String CASES_CREATE_SERVICE_REPORT = "paas.udobj.cases_create_service_report";
    String PRINT_DEVICE_CODE = "paas.udobj.print_device_code";
    String MERGE_PRINT_DEVICE_CODE = "paas.udobj.merge_print_device_code";
    String DEVICE_REGISTER = "paas.udobj.device_register";
    String SERVICE_REQUEST_FINISH = "paas.udobj.service_request_finish";
    String CONFIRM_RECEIVING = "paas.udobj.confirm_receiving";
    String SERVICE_AGREEMENT_CONFIRM_EFFECT = "paas.udobj.service_agreement_confirm_effect";
    String PRINT_QR_CODE = "paas.udobj.print_qr_code";
    String MERGE_PRINT_QR_CODE = "paas.udobj.merge_print_qr_code";
    String QR_CODE_REGISTER = "paas.udobj.qr_code_register";
    String FAULT_KNOWLEDGE_RECOMMEND = "paas.udobj.fault_knowledge_recommend";
    String ADD_SERVICE_PROVIDER = "paas.udobj.add_service_provider";
    String RKV_MARKED_DUPLICATE = "paas.udobj.rkv_marked_duplicate";
    String RKV_NO_ACTION_NEEDED = "paas.udobj.rkv_no_action_needed";

    String BPM_MANAGER = "paas.udobj.bpm_manager";
    String STAGE_MANAGER = "paas.udobj.stage_manager";
    String ACTION_TYPE_CHANGE = "paas.udobj.change";
    String HISTORY_APPLY = "paas.udobj.history_apply";

    String ADVANCED_FORMULAS = "pass.udobj.action.advanced_formulas";
    String CREATE_BOM = "pass.udobj.action.create_bom";
    String SET_ATTR_GROUP = "pass.udobj.action.set.attr.group";
    String SET_ATTR_RANGE = "pass.udobj.action.set.attr.range";

    //765
    String GDPR_BUTTON_DEFAULT = "paas.udobj.gdpr_button_default";
    String GDPR_UNABLE_OPERATION = "paas.udobj.gdpr_unable_operation";
    String GDPR_ADD_PROJECT_REQUEST_DUPLICATE = "paas.udobj.gdpr_add_project_request_duplicate";
    String GDPR_OPEN_SUCCESS = "paas.udobj.gdpr_open_success";
    String GDPR_CLOSE_SUCCESS = "paas.udobj.gdpr_close_success";
    String GDPR_EXPORT_NOT_SUPPORT = "paas.udobj.gdpr_export_not_support";
    String GDPR = "paas.udobj.gdpr";
    String GDPR_LEGAL_BASE_DETAIL = "paas.udobj.gdpr_legal_base_detail";
    String GDPR_ADD_LEGAL_BASE_DETAIL = "paas.udobj.gdpr_add_legal_base_detail";
    String GDPR_ADD_LEGAL_BASE_DETAIL_2 = "paas.udobj.gdpr_add_legal_base_detail_2";
    String GDPR_CREATE_LEGAL_BASE_LINK = "paas.udobj.gdpr_create_legal_base_link";
    String GDPR_DELETE_LEGAL_BASE_DETAIL = "paas.udobj.gdpr_delete_legal_base_detail";
    String GDPR_LEGAL_BASE = "paas.udobj.gdpr_legal_base";
    String GDPR_PERSONAL_FIELD_NUM = "paas.udobj.gdpr_personal_field_num";
    String GDPR_PROJECT_REQUEST_CLOSE = "paas.udobj.gdpr_project_request_close";
    String GDPR_LEGAL_BASE_REMARK_LIMIT = "paas.udobj.gdpr_legal_base_remark_limit";
    String GDPR_PARAMS_ERROR = "paas.udobj.gdpr_params_error";
    String GDPR_LEGAL_BASE_AGREE = "paas.udobj.gdpr_legal_base_agree";
    String GDPR_LEGAL_BASE_LEGAL_BASIS = "paas.udobj.gdpr_legal_base_legalBasis";
    String GDPR_LEGAL_BASE_CONTRACT = "paas.udobj.gdpr_legal_base_contract";
    String GDPR_LEGAL_BASE_LEGAL_OBLIGATION = "paas.udobj.gdpr_legal_base_legalObligation";
    String GDPR_LEGAL_BASE_IMPORTANT_INTERESTS = "paas.udobj.gdpr_legal_base_importantInterests";
    String GDPR_LEGAL_BASE_COMMON_INTEREST = "paas.udobj.gdpr_legal_base_commonInterest";
    String GDPR_LEGAL_BASE_PHONE = "paas.udobj.gdpr_legal_base_phone";
    String GDPR_LEGAL_BASE_EMAIL = "paas.udobj.gdpr_legal_base_email";
    String GDPR_LEGAL_BASE_UPDATE_FAILED = "paas.udobj.gdpr_legal_base_update_failed";
    String GDPR_LEGAL_BASE_SUCCESS = "paas.udobj.gdpr_legal_base_success";

    String TENANT_SCENE_SPECIFY = "paas.udobj.tenant_scene_specify";
    String CREATE_TENANT_SCENE = "paas.udobj.create_tenant_scene";
    String UPDATE_TENANT_SCENE = "paas.udobj.update_tenant_scene";
    String ENABLE_TENANT_SCENE = "paas.udobj.enable_tenant_scene";
    String DISABLE_TENANT_SCENE = "paas.udobj.disable_tenant_scene";
    String DELETE_TENANT_SCENE = "paas.udobj.delete_tenant_scene";
    String SET_SCENE_PRIORITY = "paas.udobj.set_scene_priority";

    String CUSTOM_SCENE_SPECIFY = "paas.udobj.custom_scene_specify";
    String DELETE_CUSTOM_SCENE = "paas.udobj.delete_custom_scene";


    String FUNC_RELATION_INSERT_IMPORT = "paas.udobj.func_relation_insert_import";
    String FUNC_RELATION_UPDATE_IMPORT = "paas.udobj.func_relation_update_import";
    String DISPLAY_NAME_FIELD_ADD_FAILED = "paas.udobj.display_name_field_add_failed";
    String EXPRESSION_DATE_TIME_ZONE_ATTRIBUTE_INCONSISTENT = "paas.udobj.expression_date_time_zone_attribute_inconsistent";

    String ENTER_ACCOUNT = "paas.udobj.action.enter_account";
    String UPDATE_FUNCTION_REFERENCE = "paas.udobj.update_function_reference";

    /**
     * 电子签签署按钮多语
     */
    String SIGN_FILE = "paas.udobj.action.sign_file";
    String BULK_E_SIGN_FILE = "paas.udobj.action.bulk_e_sign_file";

    String UPDATE_IMPORT_NOT_EXIST_FIELD = "paas.udobj.update_import_not_exist_field";
    String UPDATE_IMPORT_NOT_EXIST_SPECIFIED_FIELD = "paas.udobj.update_import_not_exist_specified_field";
    String UPDATE_IMPORT_MATCH_FIELD_NOT_IN_TEMPLATE = "paas.udobj.update_import_field_not_in_template";
    String UPDATE_IMPORT_NON_UNIQUE_FIELD = "paas.udobj.update_import_non_unique_field";
    String TEAM_MEMBER_UPGRADE = "paas.udobj.team_member_upgrade";

    String DISPLAY_FIELDS_ONLY_FORMULA_CAN_USED = "paas.udobj.display_fields_only_formula_can_used";

    String DIMENSION_NOT_FOUND = "paas.udobj.dimension_not_found";
    String DIMENSION_VALUE = "paas.udobj.dimension_value";
    String DIMENSION_CODE = "paas.udobj.dimension_code";

    //775 新加按钮
    String ACTION_BUDGET_TRANSFER = "paas.udobj.action.budget.transfer";
    String ACTION_BUDGET_TRANSFER_IN = "paas.udobj.action.budget.transfer.in";
    String ACTION_BUDGET_TRANSFER_OUT = "paas.udobj.action.budget.transfer.out";

    String EXTERNAL_OWNER_NON_DOWNSTREAM_CONTACT_PERSON = "paas.udobj.external_owner_non_downstream_contact_person";
    String OUTER_OWNER_NOT_DOWNSTREAM_PERSON = "paas.udobj.out_owner_not_downstream_person";
    //775 添加到价目表
    String ACTION_ADD_TO_PRICEBOOK = "paas.udobj.action.add.to.pricebook";
    String FIELD_DATA_TYPE_WRONG = "paas.udobj.field_data_type_wrong";
    String EVENT_NOT_EXIST = "paas.udobj.event_not_exist";
    String EXCEL_PRINT_TEMPLATE_NOT_EXIST = "paas.udobj.excel_print_template_not_exist";
    String PRINT_TEMPLATE_NOT_EXIST = "paas.udobj.print_template_not_exist";
    String FILTER_FIELD_VALUE_TYPE_WRONG = "paas.udobj.filter_filed_value_type_wrong";

    String LOOK_UP_FIELD_FILTER_VARIABLE_WRONG = "paas.udobj.look_up_field_filter_variable_wrong";

    String GANTT_VIEW = "paas.udobj.action.gantt_view";
    String KANBAN_VIEW = "paas.udobj.action.kanban_view";
    String RESOURCE_VIEW = "paas.udobj.action.resource_view";
    String DNB_COMMERCIAL_INFOR_QUERY = "paas.udobj.action.dnb_commercial_infor_query";
    String EYE_COMMERCIAL_INFOR_QUERY = "paas.udobj.action.eye_commercial_infor_query";

    String CREATE_CHECKINS = "paas.udobj.action.create_checkins";
    String CLOSE_TPM_ACTIVITY = "paas.udobj.action.close.tpm.activity";
    String TPM_PROOF_RANDOM_AUDIT = "paas.udobj.action.tpm.proof.random.audit";
    String GOODS_DELIVERY = "paas.udobj.action.goods_delivery";
    String STORE_COPY = "paas.udobj.action.store_copy";
    String STORE_TRANSFER = "paas.udobj.action.store_transfer";

    String PAY_INSTANTLY = "paas.udobj.action.pay_instantly";
    String CONFIRM_RECEIPT2 = "paas.udobj.action.confirm_receipt2";
    String BUY_AGAIN = "paas.udobj.action.buy_again";
    String JOINROUT = "paas.udobj.action.joinrout";
    String ONLINE_PAY = "paas.udobj.action.online_pay";
    String QR_CODE = "paas.udobj.action.qr_code";
    String ACTION_INIT = "paas.udobj.action.init";

    /**
     * 独立配置时，移动端字段展示至少1个
     */
    String MOBILE_FIELD_NOT_LESS_ONE = "pass.udobj.mobile_field_not_less_one";

    /**
     * 独立配置时，存在不可用的IncludeField
     */
    String MOBILE_FIELD_NOT_AVAILABLE = "pass.udobj.mobile_field_not_available";

    String FIELD_DATA_OVER_SIZE = "paas.udobj.field_data_over_size";

    /**
     * 入参反序列化时，jackson jsonToObj 失败
     */
    String ARGUMENT_DECODE_FROM_JSON = "pass.udobj.argument_decode_from_json";

    /**
     * 没有必要元素导致无法继续进行
     */
    String NOT_ELEMENT_PRESENT_TO_CONTINUE = "pass.udobj.not_element_present_to_continue";

    //775 深研业务
    String START_ENTERPRISE_RELATION = "paas.udobj.action.start_enterprise_relation";
    String STOP_ENTERPRISE_RELATION = "paas.udobj.action.stop_enterprise_relation";
    String START_PUBLIC_EMPLOYEE = "paas.udobj.action.start_public_employee";
    String STOP_PUBLIC_EMPLOYEE = "paas.udobj.action.stop_public_employee";

    String MASTER_DETAIL_UNSUPPORT_RELATION_OUT_DATA = "paas.udobj.master_detail_unsupport_relation_out_data";
    String RELATION_OUT_DATA_FIELD_COUNT = "paas.udobj.relation_out_data_field_count";
    String NOT_OPEN_ENTERPRISE_INTERCONNECTION = "paas.udobj.not_open_enterprise_interconnection";

    String WHAT_LIST_DATA_EXCEED_MAX_COUNT = "paas.udobj.what_list_data_exceed_max_count";
    String LIST_LAYOUT_COMPONENT_INFO_ERROR = "paas.udobj.list_layout_component_info_error";

    String BUTTON_NOT_SUPPORT_UI_EVENT = "paas.udobj.button_not_support_ui_event";
    String LOG_OPERATION_OBJECT = "paas.udobj.log_operation_object";
    String LOG_OPERATION_TYPE = "paas.udobj.log_operation_type";

    /**
     * 该选项值存在历史数据引用，不可删除，处理完引用历史数据可删除
     */
    String OPTION_NOT_REMOVE = "pass.udobj.option_not_remove";

    String FUNCTION_DOES_NOT_EXIST = "paas.udobj.function_does_not_exist_or_has_been_deleted";

    String NO_CONTACT_PERSON = "paas.udobj.no_contact_person";
    /**
     * 拓展名不合法
     */
    String INVALID_EXTENSION = "pass.udobj.invalid_extension";
    /**
     * 文件不存在或已过期，请重新上传
     */
    String FILE_NOT_EXIST_OR_EXPIRED = "pass.udobj.file_not_exist_or_expired";

    String INCORRECT_CALCULATION_RELATIONSHIP = "paas.udobj.incorrect_calculation_relationship";

    String FUNCTION_API_NAME_ALREADY_EXIST = "paas.udobj.function_api_name_already_exist";

    String FILTER_OPERATOR_WRONG = "paas.udobj.filter_operator_wrong";
    String MASTER_DETAIL_FIELD_NOT_EXIST = "paas.udobj.master_detail_field_not_exist";

    String OPTION_SET_REFERENCE_RELATION = "paas.udobj.option_set_reference_relation";
    String OPTION_SET_NOT_EXIST_OR_DELETED = "paas.udobj.option_set_not_exist_or_deleted";
    String OPTION_SET_MAX_OPTIONS = "paas.udobj.option_set_max_options";
    String OPTION_SET_MAX_LIMIT = "paas.udobj.option_set_max_limit";
    String OPTION_SET_NAME_DUPLICATE = "paas.udobj.option_set_name_duplicate";
    String FIELD_CANNOT_REPLACE_OPTION_SET = "paas.udobj.field_cannot_replace_option_set";

    String LOG_ANALYSIS_FUNCTION = "paas.udobj.log_analysis_function";

    String EXPORT_API_OBJECT_AND_FIELD = "paas.udobj.export_api_object_and_field";
    String EXPORT_API_HEADER_OBJECT_LABEL = "paas.udobj.export_api_header_object_label";
    String EXPORT_API_HEADER_OBJECT_API_NAME = "paas.udobj.export_api_header_object_api_name";
    String EXPORT_API_HEADER_FIELD_LABEL = "paas.udobj.export_api_header_field_label";
    String EXPORT_API_HEADER_FIELD_API_NAME = "paas.udobj.export_api_header_field_api_name";
    String EXPORT_API_HEADER_FIELD_TYPE = "paas.udobj.export_api_header_field_type";
    String EXPORT_API_HEADER_FIELD_IS_REQUIRED = "paas.udobj.export_api_header_field_is_required";
    String EXPORT_API_HEADER_FIELD_OPTION = "paas.udobj.export_api_header_field_option";
    String EXPORT_API_HEADER_OBJECT_REFERENCE_API_NAME = "paas.udobj.export_api_header_object_reference_api_name";
    String EXPORT_API_HEADER_HELP_TEXT = "paas.udobj.export_api_header_help_text";

    String EXPORT_API_TEXT_TYPE = "paas.udobj.export_api_text_type";
    String EXPORT_API_LONG_TEXT_TYPE = "paas.udobj.export_api_long_text_type";
    String EXPORT_API_HTML_RICH_TEXT_TYPE = "paas.udobj.export_api_html_rich_text_type";
    String EXPORT_API_SELECT_ONE_TYPE = "paas.udobj.export_api_select_one_type";
    String EXPORT_API_SELECT_MANY_TYPE = "paas.udobj.export_api_select_many_type";
    String EXPORT_API_NUMBER_TYPE = "paas.udobj.export_api_number_type";
    String EXPORT_API_CURRENCY_TYPE = "paas.udobj.export_api_currency_type";
    String EXPORT_API_DATE_TYPE = "paas.udobj.export_api_date_type";
    String EXPORT_API_TIME_TYPE = "paas.udobj.export_api_time_type";
    String EXPORT_API_DATE_TIME_TYPE = "paas.udobj.export_api_date_time_type";
    String EXPORT_API_PHONE_NUMBER_TYPE = "paas.udobj.export_api_phone_number_type";
    String EXPORT_API_IMAGE_TYPE = "paas.udobj.export_api_image_type";
    String EXPORT_API_FILE_ATTACHMENT_TYPE = "paas.udobj.export_api_file_attachment_type";
    String EXPORT_API_BIG_FILE_ATTACHMENT_TYPE = "paas.udobj.export_api_big_file_attachment_type";
    String EXPORT_API_EMAIL_TYPE = "paas.udobj.export_api_email_type";
    String EXPORT_API_TRUE_OR_FALSE_TYPE = "paas.udobj.export_api_true_or_false_type";
    String EXPORT_API_PERCENTILE_TYPE = "paas.udobj.export_api_percentile_type";
    String EXPORT_API_URL_TYPE = "paas.udobj.export_api_url_type";
    String EXPORT_API_OBJECT_REFERENCE_TYPE = "paas.udobj.export_api_object_reference_type";
    String EXPORT_API_OBJECT_REFERENCE_MANY_TYPE = "paas.udobj.export_api_object_reference_many_type";
    String EXPORT_API_AUTO_NUMBER_TYPE = "paas.udobj.export_api_auto_number_type";
    String EXPORT_API_FORMULA_TYPE = "paas.udobj.export_api_formula_type";
    String EXPORT_API_DEPARTMENT_TYPE = "paas.udobj.export_api_department_type";
    String EXPORT_API_DEPARTMENT_MANY_TYPE = "paas.udobj.export_api_department_many_type";
    String EXPORT_API_EMPLOYEE_TYPE = "paas.udobj.export_api_employee_type";
    String EXPORT_API_OUT_EMPLOYEE_TYPE = "paas.udobj.export_api_out_employee_type";
    String EXPORT_API_EMPLOYEE_MANY_TYPE = "paas.udobj.export_api_employee_many_type";
    String EXPORT_API_LOCATION_TYPE = "paas.udobj.export_api_location_type";
    String EXPORT_API_MASTER_DETAIL_TYPE = "paas.udobj.export_api_master_detail_type";
    String EXPORT_API_COUNT_TYPE = "paas.udobj.export_api_count_type";
    String EXPORT_API_SIGNATURE_TYPE = "paas.udobj.export_api_signature_type";
    String EXPORT_API_QUOTE_TYPE = "paas.udobj.export_api_quote_type";
    String EXPORT_API_COUNTRY_TYPE = "paas.udobj.export_api_country_type";
    String EXPORT_API_PROVINCE_TYPE = "paas.udobj.export_api_province_type";
    String EXPORT_API_CITY_TYPE = "paas.udobj.export_api_city_type";
    String EXPORT_API_DISTRICT_TYPE = "paas.udobj.export_api_district_type";
    String EXPORT_API_TOWN_TYPE = "paas.udobj.export_api_town_type";
    String EXPORT_API_VILLAGE_TYPE = "paas.udobj.export_api_village_type";
    String EXPORT_API_DIMENSION_TYPE = "paas.udobj.export_api_dimension_type";
    String EXPORT_API_GROUP_TYPE_AREA_TYPE = "paas.udobj.export_api_group_type_area_type";
    String EXPORT_API_GROUP_TYPE_PAYMENT_TYPE = "paas.udobj.export_api_group_type_payment_type";
    String EXPORT_API_GROUP_TYPE_SIGN_IN_TYPE = "paas.udobj.export_api_group_type_sign_in_type";
    String EXPORT_API_GROUP_TYPE_DATE_TIME_RANGE_TYPE = "paas.udobj.export_api_group_type_date_time_range_type";
    String EXPORT_API_IS_REQUIRED_TRUE = "paas.udobj.export_api_is_required_true";
    String EXPORT_API_IS_REQUIRED_FALSE = "paas.udobj.export_api_is_required_false";


    String EXPORT_LOGIN_EXCEPTION_1 = "paas.udobj.export_login_exception_1";
    String EXPORT_LOGIN_EXCEPTION_2 = "paas.udobj.export_login_exception_2";
    String EXPORT_LOGIN_STATUS_0 = "paas.udobj.export_login_status_0";
    String EXPORT_LOGIN_STATUS_1 = "paas.udobj.export_login_status_1";
    String EXPORT_LOGIN_TYPE_0 = "paas.udobj.export_login_type_0";
    String EXPORT_LOGIN_TYPE_1 = "paas.udobj.export_login_type_1";
    String EXPORT_LOGIN_TYPE_2 = "paas.udobj.export_login_type_2";
    String EXPORT_LOGIN_TYPE_3 = "paas.udobj.export_login_type_3";
    String EXPORT_LOGIN_TYPE_4 = "paas.udobj.export_login_type_4";
    String EXPORT_LOGIN_TYPE_5 = "paas.udobj.export_login_type_5";
    String EXPORT_LOGIN_TYPE_6 = "paas.udobj.export_login_type_6";
    String EXPORT_LOGIN_TYPE_7 = "paas.udobj.export_login_type_7";
    String EXPORT_LOGIN_TYPE_8 = "paas.udobj.export_login_type_8";
    String EXPORT_LOGIN_TYPE_9 = "paas.udobj.export_login_type_9";
    String EXPORT_LOGIN_TYPE_10 = "paas.udobj.export_login_type_10";
    String EXPORT_LOGIN_TYPE_11 = "paas.udobj.export_login_type_11";
    String EXPORT_LOGIN_TYPE_12 = "paas.udobj.export_login_type_12";
    String EXPORT_LOGIN_TYPE_13 = "paas.udobj.export_login_type_13";
    String EXPORT_LOGIN_TYPE_14 = "paas.udobj.export_login_type_14";
    String EXPORT_LOGIN_TYPE_15 = "paas.udobj.export_login_type_15";
    String EXPORT_LOGIN_TYPE_16 = "paas.udobj.export_login_type_16";
    String EXPORT_LOGIN_TYPE_17 = "paas.udobj.export_login_type_17";
    String EXPORT_LOGIN_TYPE_18 = "paas.udobj.export_login_type_18";
    String EXPORT_LOGIN_TYPE_19 = "paas.udobj.export_login_type_19";
    String EXPORT_LOGIN_TYPE_20 = "paas.udobj.export_login_type_20";
    String EXPORT_LOGIN_TYPE_21 = "paas.udobj.export_login_type_21";
    String EXPORT_LOGIN_TYPE_22 = "paas.udobj.export_login_type_22";
    String EXPORT_LOGIN_TYPE_FXIAOKE = "paas.udobj.export_login_type_fxiaoke";
    String EXPORT_LOGIN_TYPE_WECHAT = "paas.udobj.export_login_type_wechat";
    String EXPORT_LOGIN_TYPE_MINI_PROGRAM = "paas.udobj.export_login_type_mini_program";
    String EXPORT_LOGIN_TYPE_THIRD_PARTY = "paas.udobj.export_login_type_third_party";
    String EXPORT_CRM_LEVEL_2 = "paas.udobj.export_crm_level_2";
    String EXPORT_CRM_LEVEL_3 = "paas.udobj.export_crm_level_3";
    String EXPORT_CRM_LEVEL_1 = "paas.udobj.export_crm_level_1";
    String EXPORT_LOGIN_HEADER_PERSON = "paas.udobj.export_login_header_person";
    String EXPORT_LOGIN_HEADER_DEPARTMENT = "paas.udobj.export_login_header_department";
    String EXPORT_LOGIN_HEADER_OWNER = "paas.udobj.export_login_header_owner";
    String EXPORT_LOGIN_HEADER_BROWSER = "paas.udobj.export_login_header_browser";
    String EXPORT_LOGIN_HEADER_TIME = "paas.udobj.export_login_header_time";
    String EXPORT_LOGIN_HEADER_TYPE = "paas.udobj.export_login_header_type";
    String EXPORT_LOGIN_HEADER_STATUS = "paas.udobj.export_login_header_status";
    String EXPORT_LOGIN_HEADER_EXCEPTION = "paas.udobj.export_login_header_exception";
    String EXPORT_AUDITLOG_HEADER_PERSON = "paas.udobj.export_auditlog_header_person";
    String EXPORT_AUDITLOG_HEADER_DATA = "paas.udobj.export_auditlog_header_data";
    String EXPORT_AUDITLOG_HEADER_LEVEL = "paas.udobj.export_auditlog_header_level";
    String EXPORT_AUDITLOG_HEADER_MODULE = "paas.udobj.export_auditlog_header_module";
    String EXPORT_AUDITLOG_HEADER_TIME = "paas.udobj.export_auditlog_header_time";
    String EXPORT_AUDITLOG_HEADER_OPERATION_OBJECT = "paas.udobj.export_auditlog_header_operation_object";
    String EXPORT_AUDITLOG_HEADER_BIZ_OPERATION_NAME = "paas.udobj.export_auditlog_header_biz_operation_name";
    String EXPORT_AUDITLOG_HEADER_OBJECT = "paas.udobj.export_auditlog_header_object";
    String EXPORT_AUDITLOG_HEADER_TEXT_MESSAGE = "paas.udobj.export_auditlog_header_text_message";
    String EXPORT_AUDITLOG_HEADER_DATA_ID = "paas.udobj.export_auditlog_header_data_id";
    String EXPORT_AUDITLOG_HEADER_JSON_MESSAGE = "paas.udobj.export_auditlog_header_json_message";
    String EXPORT_AUDITLOG_HEADER_MODIFY_DATALIST = "paas.udobj.export_auditlog_header_modify_datalist";
    //正在处理中，请稍候再查看结果
    String PROCESSING_ALERT = "paas.udobj.processing_alert";
    String PREVIOUS_REQUEST_PROCESSING_ALERT = "paas.udobj.previous_request_processing_alert";
    String CREATE_TEAM_MEMBER_NO_PERMISSION = "paas.udobj.create_team_member_no_permission";
    String CREATE_TEAM_MEMBER_READ_PERMISSION = "paas.udobj.create_team_member_read_permission";
    String CREATE_TEAM_MEMBER_WRITE_PERMISSION = "paas.udobj.create_team_member_write_permission";
    String CREATE_TEAM_MEMBER_CREATOR_JOIN_TEAM_MEMBER = "paas.udobj.create_team_member_creator_join_team_member";

    String COMPONENT_SETTINGS_ERROR = "paas.udobj.component_settings_error";
    String RELATED_LIST_FORM_COMPONENT_SETTINGS_ERROR = "paas.udobj.related_list_form_component_settings_error";
    String MASTER_DETAIL_NOT_SUPPORT_RELATED_LIST_FORM_COMPONENT = "paas.udobj.master_detail_not_support_related_list_form_component";

    String NAME_NOT_UNIQUE = "paas.udobj.name_not_unique";
    String DATA_NOT_FIND = "paas.udobj.data_not_find";
    String REFERENCE_MANY_TYPE_ERROR = "paas.udobj.reference_many_type_error";
    String REFERENCE_MANY_VALUE_TYPE_ERROR = "paas.udobj.reference_many_value_type_error";

    String REPORT_ANALYSIS = "paas.udobj.button.report_analysis";

    String BATCH_TRANSFER_PROCUREMENT_ACCOUNT = "paas.udobj.button.batch_transfer_procurement_account";
    String BATCH_TRANSFER_PROCUREMENT_LEADS = "paas.udobj.button.batch_transfer_procurement_leads";
    String TRANSFER_ALL_PROCUREMENT_ACCOUNT = "paas.udobj.button.transfer_all_procurement_account";
    String TRANSFER_ALL_PROCUREMENT_LEADS = "paas.udobj.button.transfer_all_procurement_leads";
    String TRANSFER_PROCUREMENT_ACCOUNT = "paas.udobj.button.transfer_procurement_account";
    String TRANSFER_PROCUREMENT_LEADS = "paas.udobj.button.transfer_procurement_leads";

    String TRANSFER_PROCUREMENT = "paas.udobj.button.transfer_procurement";
    String ALLOCATE_PROCUREMENT = "paas.udobj.button.allocate_procurement";

    String BULK_CHOOSE_PROCUREMENT = "paas.udobj.button.bulk_choose_procurement";
    String RELATED_PROCUREMENT = "paas.udobj.button.related_procurement";

    String INHERIT_WECHAT = "paas.udobj.button.inherit_wechat";
    String FRIEND_INHERIT_WECHAT = "paas.udobj.button.friend_inherit_wechat";
    String GROUP_INHERIT_WECHAT = "paas.udobj.button.group_inherit_wechat";
    String RELATED_EMPLOYEE = "paas.udobj.button.related_employee";
    String JUDGE_QIRESULT = "paas.udobj.button.judge_qi_result";

    String SALES_ORDER_COST_ASSIGN = "paas.udobj.button.sales_order_cost_assign";

    String ACTION_AGREEMENT_STORE_CONFIRM = "paas.udobj.action.agreement_store_confirm";
    String BUDGET_CARRY_OVER = "paas.udobj.action.budget.carry.over";
    String BATCH_MANAGE_PICTURE = "paas.udobj.action.batch_manage_picture";
    String LAYOUT_TYPE_NOT_SUPPORT_EXCEPTION = "paas.udobj.layout_type_not_support_exception";
    String BUDGET_TAKE_APART = "paas.udobj.action.budget.take.apart";
    String COST_WRITE_OFF = "paas.udobj.action.cost_write_off";

    String RE_ANALYSIS_RFM = "paas.udobj.button.rfm.redo";

    String CONFIG_ALREADY_EXIST = "paas.udobj.config_already_exist";

    String RFM_RULE_DISPLAY_NAME = "RFMRuleObj.attribute.self.display_name";

    String CAN_NOT_ASSOCIATED_YOURSELF = "paas.udobj.can_not_associated_yourself";

    String CAN_NOT_ASSOCIATED_CHILD_OR_PARENT_DATA = "paas.udobj.can_not_associated_child_or_parent_data";

    String DATA_HIERARCHY_CAN_NOT_EXCEED = "paas.udobj.data_hierarchy_can_not_exceed";

    String MAXIMUM_OF_NUM_CAN_BE_CREATED = "paas.udobj.maximum_of_num_can_be_created";
    String MAXIMUM_OF_CHILD_CAN_BE_CREATED = "paas.udobj.maximum_of_child_can_be_created";

    String DATA_EXIST_SUB_LEVEL_NOT_SUPPORT_INVALID = "paas.udobj.data_exist_sub_level_not_support_invalid";

    String AREA_BATCH_EDIT = "paas.udobj.action.areaBatchEdit";
    String AREA_BATCH_CHECK = "paas.udobj.action.areaBatchCheck";
    String AREA_BATCH_ADD = "paas.udobj.action.areaBatchAdd";
    String AREA_MERGE = "paas.udobj.action.areaMerge";

    String REACHED_MAX_LIMIT_SIZE = "paas.udobj.reached_max_limit_size";

    String TREE_VIEW = "paas.udobj.tree_view";
    String OBJECT_INVALID = "paas.udobj.object_invalid";
    String FUNCTION_PLUGIN_FAIL = "paas.udobj.function_plugin_fail";
    String DATA_FIELD = "paas.udobj.data_field";
    String LAYOUT_BASIC_INFO = "paas.metadata.layout.base_info";
    String UPDATE_IMPORT_MATCH_FIELD_DATA = "paas.udobj.update_import_match_type_field_data";

    String BALANCE_REDUCE = "paas.udobj.action.balance_reduce";
    String MULTI_ACCOUNT_REDUCE = "paas.udobj.action.multi_account_reduce";
    String CALL_OUT = "paas.udobj.action.call_out";
    String STATISTIC_TABLE_REFRESH = "paas.udobj.action.budget.statistic.table.refresh";
    String ACTION_COPY = "paas.udobj.action.copy";
    String ENABLE_BUDGET = "paas.udobj.action.budget.eable";
    String SEND_AS_TEMPLATE = "paas.udobj.action.send_as_template";
    String SEND_ATTACHMENT = "paas.udobj.action.send_attachment";
    /**
     * {0}正在计算，保存后会停止计算，以此次计算范围为准
     */
    String CALC_JOB_FOR_FIELD_NOT_END = "paas.udobj.calc_job_for_field_not_end";

    /**
     * CSM: Customer Success Manager（客户成功经理）
     * 「{0}」数据量过大，新建「{1}」可能会有性能风险，请您联系企业CSM走特殊审批处理
     */
    String CALC_JOB_FOR_MAX_COUNT_DATA_TOO_LARGE = "paas.udobj.calc_job_for_max_count_data_too_large";

    String SYSTEM_OPTION_SET_CANNOT_MODIFY = "paas.udobj.system_option_set_cannot_modify";
    // define_type 值非法, 联系研发人员处理(null, custom, package之一)
    String OPTION_SET_DEFINE_TYPE_NO_EXIT = "paas.udobj.option_set_define_type_no_exit";
    /**
     * {0}布局中缺少基本信息，请重新调整该布局并保存。
     */
    String LAYOUT_LACK_BASE_FIELD_SECTION = "paas.udobj.layout_lack_base_field_section";

    /**
     * ocr识别超时请稍后再试。
     */
    String OCR_TIME_OUT = "paas.udobj.ocr_time_out";
    /**
     * ocr识别失败请稍后再试。
     */
    String OCR_EXCEPTION = "paas.udobj.ocr_exception";

    String RESET_PUBLIC_EMPLOYEE_PASSWORD = "paas.udobj.action.reset_public_employee_password";


    String ADD_SUPERIOR = "paas.udobj.action.Add.Superior";

    String ADD_SUBORDINATE = "paas.udobj.action.Add.Subordinate";

    String RELIEVE_SUPERIOR = "paas.udobj.action.Relieve.Superior";

    String ENTERPRISE_WECHAT_IMPORT = "paas.udobj.action.enterprise_wechat_import";

    String ENTERPRISE_WECHAT_ADD = "paas.udobj.action.enterprise_wechat_add";

    String ENTERPRISE_WECHAT_RELEVANCE = "paas.udobj.action.enterprise_wechat_relevance";

    String HAS_BEEN_DISCONTINU = "paas.udobj.has_been_discontinu";

    String TENANT_NOT_GRAY_MULTIPLE_TIME_ZONES_CANNONT_USE_ATTRIBUTE = "paas.udobj.tenant_not_gray_multiple_time_zones_cannont_use_attribute";

    String NOT_USE_MULTI_TIME_ZONE_CANNOT_CHANGE = "paas.udobj.not_use_multi_time_zone_cannot_change";

    String OWNER_REQUIRED_CAN_NOT_BE_EMPTY = "paas.udobj.owner_required_can_not_be_empty";

    String TEMPORARY_DOES_NOT_SUPPORT_EMPTY_OWNER = "paas.udobj.temporary_does_not_support_empty_owner";

    String print_receipt = "paas.udobj.action.print_receipt";
    String conformance_statements = "paas.udobj.action.conformance_statements";

    String transferLeads = "paas.udobj.action.transferLeads";

    String transferNewOpportunity = "paas.udobj.action.transferNewOpportunity";

    String transferAccount = "paas.udobj.action.transferAccount";

    String transferPartnerPro = "paas.udobj.action.transferPartnerPro";
    String transferCompetitor = "paas.udobj.action.transferCompetitor";
    String BUDGET_ACCRUAL = "paas.udobj.action.budget.accrual";
    String BUDGET_CLOSURE = "paas.udobj.action.budget.closure";
    String GOODS_RECEIVED_NOTE_GENERATED = "paas.udobj.action.goods_received_note_generated";
    String OUTBOUND_NOTE_GENERATED = "paas.udobj.action.outbound_note_generated";
    String IMPORT_UPDATE = "paas.udobj.action.import_update";
    String RETURN_RECEIPT = "paas.udobj.action.return_receipt";
    /**
     * 【{0}】对象已关闭相关对象能力，无法对相关对象进行操作
     */
    String RELATED_TEAM_SWITCH_IS_CLOSED = "paas.udobj.related_team_switch_is_closed";
    /**
     * 已有历史数据使用了相关团队，请先处理历史数据再关闭
     */
    String HISTORICAL_DATA_USED_RELATED_TEAM = "paas.udobj.historical_data_used_related_team";
    /**
     * 该对象已关闭了相关团队功能，请重新设置关联外部数据权限配置项
     */
    String RESET_RELATION_OUTER_DATA_PRIVILEGE_CONFIGURATION = "paas.udobj.reset_relation_outer_data_privilege_configuration";

    /**
     * 可选功能：{0}相关团队能力，{1}全局搜索能力，{2}跟进动态能力，{3}修改记录能力，{4}多字段排序，{5}跨对象筛选
     */
    String OPTIONAL_FEATURES_DETAILS = "paas.udobj.optional_features_details";

    /**
     * 开启
     */
    String SWITCH_ENABLE = "paas.udobj.switch_enable";
    /**
     * 关闭
     */
    String SWITCH_CLOSE = "paas.udobj.switch_close";

    String MOBILE_NOT_SUPPORT = "paas.udobj.mobile_not_support";
    String NOT_SUPPORT_FROM_GLOBAL_SEARCH = "paas.udobj.not_support_from_global_search";

    String CANNOT_CHANGE_OWNER_WHEN_EDIT_TEAM_MEMBER = "paas.udobj.cannot_change_owner_when_edit_team_member";
    String GDPR_AGREE_PERSON_ACCOUNT = "paas.udobj.gdpr_agree_person_account";
    String GDPR_LINK_INVALID = "paas.udobj.gdpr_link_invalid";
    String GDPR_LEGAL_BASE_CLOSE = "paas.udobj.gdpr_legal_base_close";
    String GDPR_COMPLIANCE_CLOSE = "paas.udobj.gdpr_compliance_close";
    String GDPR_LEGAL_BASE_DETAIL_AGREE_UPDATE = "paas.udobj.gdpr_legal_base_detail_agree_update";
    String CANNOT_CHANGE_TENANT_SCENE_FIELD_LIST = "paas.udobj.cannot_change_tenant_scene_field_list";

    String IMPORT_LOCATION_FORMAT_ERROR = "paas.udobj.import_location_format_error";
    String IMPORT_LOCATION_ANALYSE_ERROR = "paas.udobj.import_location_analyse_error";
    String APPROVAL_AFTER_ERROR_RETRY = "paas.udobj.action.approval_after_error_retry";
    String APPROVAL_AFTER_ERROR_IGNORE = "paas.udobj.action.approval_after_error_ignore";
    String BPM_AFTER_ERROR_RETRY = "paas.udobj.action.bpm_after_error_retry";
    String BPM_AFTER_ERROR_IGNORE = "paas.udobj.action.bpm_after_error_ignore";
    String EXPORT_FIELD_NO_PERMISSION = "paas.udobj.export_field_no_permission";
    String EXPORT_FILE_FIELD_NO_PERMISSION = "paas.udobj.export_file_field_no_permission";

    String EXPORT_FILE_ATTACHMENT_EXCEED = "paas.udobj.export_file_attachment_exceed";
    String EXPORT_NAME_LABEL = "paas.udobj.export_department_name_label";
    String EXPORT_LOOK_LOCATION_FILE = "paas.udobj.export_look_location_file";
    String DUPLICATE_SEARCH_RULES_COUNT_OVER_LIMIT = "paas.udobj.duplicate_search_rules_count_over_limit";

    String DUPLICATE_SEARCH_RULES_ENABLE_CANNOT_DELETE = "paas.udobj.duplicate_search_rules_enable_cannot_delete";


    String INITIATE_RECONCILIATION = "paas.udobj.action.initiate_reconciliation";
    String CONFIRM_RECONCILIATION = "paas.udobj.action.confirm_reconciliation";

    String DISASSEMBLY_RETRY = "paas.udobj.action.disassembly_retry";
    String DISASSEMBLY_UN_FROZEN_RETRY = "paas.udobj.action.disassembly_un_frozen_retry";

    String OBJECT_BOUND_RULE_CANNOT_BE_CHANGE = "paas.udobj.object_bound_rule_cannot_be_change";

    String TAG_GROUP_DELETED = "paas.udobj.tag_group_deleted";

    // TODO: 2023/2/5 这个话术怎么说
    String TAG_NOT_DISABLE = "paas.udobj.tag_not_disable";
    String TAG_GROUP_TYPE_IS_NOT_CHANGE = "paas.udobj.tag_group_type_is_not_change";

    String LOG_CONFIG_MESSAGE = "paas.udobj.log_config_message";

    String LOG_CONFIG_TRIGGER_WORK_FLOW = "paas.udobj.log_config_trigger_work_flow";

    String LOG_CONFIG_TRIGGER_APPROVAL_FLOW = "paas.udobj.log_config_trigger_approval_flow";


    String TAG_GROUP_BIND_RULE_CANNOT_DISABLE = "paas.udobj.tag_group_bind_rule_cannot_disable";


    String TAG_GROUP_BIND_RULE = "paas.udobj.tag_group_bind_rule";

    /**
     * #{0}#适用对象为#{1}#,
     */
    String SOURCE_RULE_BIND_OBJ = "paas.udobj.source_rule_bind_obj";
    /**
     * 请删除相关规则配置或禁用规则后，再对其禁用 tips:若禁用规则，请确认该规则下是否还有其他规则结果后，再进行操作
     */
    String DISABLE_TAG_OR_GROUP_TITLE = "paas.udobj.disable_tag_or_group";

    String UPDATE_TAG_GROUP_OBJECT_RANGE = "paas.udobj.update_tag_group_object_range";

    String TAG_GROUP = "paas.udobj.tag_group";
    String TAG = "paas.udobj.tag";

    String MAIN_CONTROL_STRATEGY = "sfa.udobj.main_control_strategy";

    String LAYOUT_REFERENCED_CANNOT_BE_DISABLED = "paas.udobj.layout_referenced_cannot_be_disabled";
    String LAYOUT_REFERENCED_CANNOT_BE_DELETED = "paas.udobj.layout_referenced_cannot_be_deleted";
    String COPY_OBJECT_NOTICE_TITLE = "paas.udobj.copy_object_notice_title";
    String COPY_OBJECT_NOTICE_MSG = "paas.udobj.copy_object_notice_msg";

    String DUPLICATED_SEARCH_RULE_UN_EFFECTIVE = "paas.udobj.duplicated_search_rule_un_effective";
    String CHANGE_RULE_MAX_LIMIT = "paas.udobj.change_rule_max_limit";
    String CHANGE_RULE_NOT_EXIST_OR_DELETED = "paas.udobj.change_rule_not_exist_or_deleted";

    // 变更按钮
    String CHANGE_ACTION = "paas.udobj.action.change";
    String IMPORT_ADD = "paas.udobj.action.import_add";
    // 生效按钮
    String EFFECTIVE_ACTION = "paas.udobj.action.effective";
    String CLOSE_SALES_ORDER = "paas.udobj.action.close_sales_order";

    String CONFIRM_ORDER = "paas.udobj.action.confirm_order";
    String CANCELLATION_ORDER = "paas.udobj.action.cancellation_order";
    String TERMINATE_ORDER = "paas.udobj.action.terminate_order";
    String CLOSE_ORDER = "paas.udobj.action.close_order";
    String JUMP_SHOP = "paas.udobj.action.jump_shop";
    String CREAT_OUT_PLAN = "paas.udobj.action.create_out_plan";
    String GENERATE_TREE_RELATION = "paas.udobj.action.generate_tree_relation";
    // 转换
    String TRANSFORM = "paas.udobj.action.transform";
    // 参照新建
    String REFERENCE_CREATE = "paas.udobj.action.reference_create";

    String CHANGE_APPROVAL_CANDIDATE_IDS = "paas.udobj.change_approval_candidate_ids";

    String CHANGE_ORDER_MASTER_DETAIL_FIELD_CHECK = "paas.udobj.change_order_master_detail_field_check";
    String CHANGE_ORDER_MASTER_DETAIL_OBJECT_CHECK = "paas.udobj.change_order_master_detail_object_check";
    String UNSUPPORTED_OPERATION = "paas.udobj.unsupported_operation";
    String CHANGED_STATUS_IS_IN_CHANGE = "paas.udobj.changed_status_is_in_change";
    String CHANGED_STATUS_NOT_INEFFECTIVE = "paas.udobj.changed_status_not_ineffective";
    String NO_AVAILABLE_CHANGE_RULES = "paas.udobj.no_available_change_rules";
    String UNSUPPORTED_MULTI_CHANGE_RULE = "paas.udobj.unsupported_multi_change_rule";
    String OPEN_CHANGE_ORDER_NOT_SUPPORT_UPDATE_IMPORT = "paas.udobj.open_change_order_not_support_update_import";
    String CHANGE_ORDER_OBJECT_NOT_SUPPORT_IMPORT = "paas.udobj.change_order_object_not_support_import";
    String NO_CHANGE_RULES_AVAILABLE = "paas.udobj.no_change_rules_available";
    String DATA_IN_CHANGE_WITH_CHANGE_RULES = "paas.udobj.data_in_change_with_change_rules";

    String IMPORT_AREA_NOT_EXIST = "paas.udobj.import_area_not_exist";
    String IMPORT_AREA_NOT_MATCH = "paas.udobj.import_area_not_match";
    String RULE_API_NAME_REPEAT = "paas.udobj.duplicated_search_rule_repeat";
    String TAG_GROUP_NOT_DISABLE = "paas.udobj.tag_group_not_disable_cannot_delete";

    String ACTION_BUDGET_CARRY_FORWARD_RETRY = "paas.udobj.action.budget_carry_forward_retry";

    String REVISIT_VIRTUAL_PHONE = "paas.udobj.revisit_virtual_phone";
    String DUPLICATE_SCENE_NAME = "paas.udobj.duplicate_scene_name";

    String FIELD_NOT_NULL = "paas.udobj.field_not_null";
    String FIELD_NOT_LESS_THAN = "paas.udobj.field_not_less_than";

    String CHANGE_OBJECT = "paas.udobj.change_object";
    String CHANGE_HISTORY = "paas.udobj.change_history";
    String ORIGINAL = "paas.udobj.original";
    String HAS_FLOW_DEFINITIONS = "paas.udobj.has_flow_definitions";
    String NOT_REPEAT_OPENING = "paas.udobj.not_repeat_opening";
    String NOT_SUPPORT_OPEN_CHANGE_ORDER = "paas.udobj.not_support_open_change_order";

    String FIELD_TYPE_ERROR = "paas.udobj.field_type_error";
    String SELECT_FIELD_FIRST_MULTI = "paas.udobj.select_field_first_mutil";

    String ORIGINAL_DATA_NOT_EXIT = "paas.udobj.original_data_not_exit";

    String PROMOTER_RESIGNED = "paas.udobj.action.promoter_resigned";
    String PROMOTER_IN_POSITION = "paas.udobj.action.promoter_in_position";
    String PROMOTER_AGREE = "paas.udobj.action.promoter_agree";
    String PROMOTER_DISAGREE = "paas.udobj.action.promoter_disagree";
    String PROMOTER_QRCODE_INVITE = "paas.udobj.action.promoter_qrcode_invite";
    String DISTRIBUTE = "paas.udobj.action.distribute";
    String CORRECTION = "paas.udobj.action.correction";
    String UPDATE_SALARY_DATA = "paas.udobj.action.UpdateSalaryData";
    String NOT_DEFINITION_APPROVAL_WITH_CHANGE_ORDER_RULE = "paas.udobj.not_definition_approval_with_change_order_rule";
    String HTML_RICH_TEXT = "paas.udobj.html_rich_text";

    /**
     * 管理员进行了场景配置，已覆盖了您个人设置
     */
    String CUSTOM_LIST_CONF_CLEAR_FROM_CRM_MANAGER_TITLE = "paas.udobj.custom_list_conf_clear_from_crm_manager";


    /**
     * 管理员调整了您{0}对象列表页场景{1}下字段设置（列宽/字段显隐/字段顺序），如有问题请联系CRM管理员处理。
     */
    String CUSTOM_LIST_SCENE_CLEAR_FROM_CRM_MANAGER_CONTENT = "paas.udobj.content.custom_list_scene_clear_from_crm_manager";

    /**
     * 管理员调整了您{0}对象列表页个人设置，如有问题请联系CRM管理员处理。
     */
    String CUSTOM_LIST_CONF_CLEAR_FROM_CRM_MANAGER_CONTENT = "paas.udobj.content.custom_list_conf_clear_from_crm_manager";
    String NODE_CHANGE = "paas.udobj.node_change";
    String SET_ROOT = "paas.udobj.set_root";
    String CONVERT_RULE_DISABLE_OR_DELETE = "paas.udobj.convert_rule_disable_or_delete";
    String DATA_NOT_MATCH_CONVERT_RULE = "paas.udobj.data_not_match_convert_rule";
    String CONVERT_RULE_API_NAME_EXIST = "paas.udobj.convert_rule_api_name_exist";
    /**
     * 转换失败，所匹配的转换规则「xxx转换规则名称」源单关闭策略中字段「xxxx」已被禁用/删除，请联系系统管理员修改。
     */
    String CONVERT_RULE_ASSOCIATED_FIELD_DISABLE_OR_DELETE = "paas.udobj.convert_rule_associated_field_disable_or_delete";
    /**
     * 转换失败，所匹配的转换规则「xxx转换规则名称」中建立关联关系的字段「xxxx」已被禁用/删除，请联系系统管理员修改。
     */
    String CONVERT_RULE_CLOSE_FIELD_DISABLE_OR_DELETE = "paas.udobj.convert_rule_close_field_disable_or_delete";

    String DATA_YOU_SELECTED_NOT_MATCH = "paas.udobj.data_you_selected_not_match";

    String CONVERT_RULE = "paas.udobj.convert_rule";
    String ADD_SPARE_PART_DELIVERY = "paas.udobj.action.add_spare_part_delivery";

    String QUERY_EQUITY_RELATIONSHIP = "paas.udobj.QueryEquityRelationship";

    String MASTER_DATA_OF_DETAIL_DATA = "paas.udobj.master_data_of_detail_data";
    String CANNOT_EDIT_WITHOUT_DATA_PERMISSION = "paas.udobj.cannot_edit_without_data_permission";

    String BLACKLIST_MARK = "paas.udobj.button.blacklist_mark";
    String BLACKLIST_CANCEL = "paas.udobj.button.blacklist_cancel";
    String RISK_MONITOR_ENABLE = "paas.udobj.button.risk_monitor_enable";
    String RISK_MONITOR_DISABLE = "paas.udobj.button.risk_monitor_disable";
    String RISK_PORTRAIT_ENABLE = "paas.udobj.button.risk_portrait_enable";
    String SYSTEM_EXISTS_OBJ = "paas.udobj.system.exists.obj";
    String ALREADY_SUPPORT_PRE_OBJ = "paas.udobj.already_support_pre_obj";
    String BULK_SHOW_ANNOUNCE = "paas.udobj.action.announce.BulkShow";
    String BULK_HIDE_ANNOUNCE = "paas.udobj.action.announce.BulkHide";
    String ACTION_CRM_SYNCDATA = "paas.udobj.action.crm_syncdata";
    String EXPORT_GANTT_CHART = "paas.udobj.export_gantt_chart";
    String GENERATE_PROJECT_BUDGET = "paas.udobj.generate_project_budget";
    String UPDATE_PERSON_BUDGET = "paas.udobj.update_person_budget";

    String Deactivate = "paas.udobj.deactivate";
    String VIEW_RULE = "paas.udobj.view_rule";


    String FOLLOW = "paas.udobj.action.Follow";
    String UNFOLLOW = "paas.udobj.action.Unfollow";
    String ONLINE_PAYMENT = "paas.udobj.action.online_payment";
    String FOLLOW_OBJECT_LIMIT = "paas.udobj.action.follow_object_limit";
    String FOLLOW_LIMIT = "paas.udobj.action.follow_limit";
    String CURRENCY_SYSTEM_NOT_EXIST = "paas.udobj.currency_system_not_exist";
    String CURRENCY_SYSTEM_ALREADY_EXIST = "paas.udobj.currency_system_already_exist";
    String CURRENCY_NOT_EXIST = "paas.udobj.currency_not_exist";
    String TEAM_MEMBER_DEPT_CASCADE_ERROR = "paas.udobj.team_member_dept_cascade_error";

    String NOT_SUPPORTED_FUNCTION_UPDATE_OPTION_SET = "paas.udobj.not_supported_function_update_option_set";
    String REFUND = "paas.udobj.action.refund";
    String FOLLOW_FAILED = "paas.udobj.follow_failed";
    String OPTION_VALUE_DUPLICATE = "paas.udobj.option_value_duplicate";

    String PURCHASE_PAY = "paas.udobj.action.purchase_pay";
    String BULK_DELETE_MASTER_LIMIT_EXCEED = "paas.udobj.bulk_delete_master_limit_exceed";

    //启用数据多语的字段类型错误:
    String ENABLE_FIELD_LANG_TYPE_ERROR = "paas.udobj.enable_field_lang_type_error";

    String ENABLE_FIELD_LANG_ERROR = "paas.udobj.enable_field_lang_error";

    String IMPORT_DATA_LANGUAGE_ERROR = "paas.udobj.import_data_lang_error";
    String FIELD_PROMPT_LENGTH_MORE_LIMIT = "paas.udobj.field_prompt_length_more_limit";

    /**
     * {0}的{1}类型不是部门，请完善{2}值再提交
     */
    String DEPT_TYPE_VALIDATE = "paas.udobj.data_own_dept_type_validate";
    String OBJECT_FIELD_IS_NOT_UNIQUE = "paas.udobj.object_field_is_not_unique";
    String OBJECT_FIELD_IS_NOT_SELECTED = "paas.udobj.object_field_is_not_selected";
    String OBJECT_FIELD_ONLY_ALLOW_ID = "paas.udobj.object_field_only_allow_id";
    String OBJECT_FIELD_ONLY_ONE = "paas.udobj.object_field_only_one";
    String OBJECT_REFERENCE_FIELD_ONLY_ONE = "paas.udobj.object_reference_field_only_one";
    String OBJECT_REFERENCE_MANY_FIELD_NOT_MATCH = "paas.udobj.object_reference_many_field_not_match";

    // 890 公共对象
    String NOT_SUPPORT_OPEN_PUBLIC_OBJECT = "paas.udobj.public_object.not_support";
    String PRIVATE_FIELD = "paas.udobj.public_object.private_field";
    String PUBLIC_FIELD_PRIVATE_DATA = "paas.udobj.public_object.public_field_private_data";
    String PUBLIC_FIELD_PUBLIC_DATA = "paas.udobj.public_object.public_field_public_data";
    String NOT_SUPPORT_PUBLIC_FIELD_CONVERT = "paas.udobj.public_object.not_support_public_field_convert";
    String CANNOT_MODIFY_SYSTEM_FIELD_PUBLIC_FIELD_FLAG = "paas.udobj.public_object.cannot_modify_system_field_public_field_flag";
    String CANNOT_MODIFY_PUBLIC_OBJECT_FLAG = "paas.udobj.public_object.cannot_modify_public_object_flag";
    String CANNOT_SET_SYSTEM_FIELD_PUBLIC_FIELD_FLAG = "paas.udobj.public_object.cannot_set_system_field_public_field_flag";
    String FIELD_USED_IN_GROUP_CANNOT_SET_PUBLIC_FIELD_FLAG = "paas.udobj.public_object.field_used_in_group_cannot_set_public_field_flag";
    String NOT_SUPPORT_PUBLIC_FIELD_WITH_FUNCTION = "paas.udobj.public_object.not_support_public_field_with_function";
    String PUBLIC_FIELD_CANNOT_DEPENDENT_ON = "paas.udobj.public_object.public_field_cannot_dependent_on";
    String PUBLIC_FIELD_CANNOT_DEPENDENT_ON_PRIVATE_OBJECT = "paas.udobj.public_object.public_field_cannot_dependent_on_private_object";
    String OBJECT_ASSOCIATED_WITH_PUBLIC_FIELD = "paas.udobj.public_object.object_associated_with_public_field";
    String ALREADY_ENABLED_PUBLIC_OBJECT = "paas.udobj.public_object.already_enabled_public_object";
    String ASSOCIATED_OBJECT_NOT_SAME_UPSTREAM = "paas.udobj.public_object.associated_object_not_same_upstream";
    String INVITES_UPGRADE_PUBLIC_OBJECT_MESSAGE = "paas.udobj.public_object.invites_upgrade_public_object_message";
    String INVITES_UPGRADE_PUBLIC_OBJECT = "paas.udobj.public_object.invites_upgrade_public_object";
    String VIEW_DETAILS = "paas.udobj.public_object.view_details";
    String SELF_MANAGED_COMPANY_CAN_NOT_UPGRADE_PUBLIC_OBJECT = "paas.udobj.public_object.self_managed_company_can_not_upgrade_public_object";
    String NOT_SELF_MANAGED_COMPANY_CAN_NOT_INVITES = "paas.udobj.public_object.not_self_managed_company_can_not_invites";
    String COMPANY_NOT_HAS_FS_ACCOUNT = "paas.udobj.public_object.company_not_has_fs_account";
    String COMPANY_RELATION_TYPE_NOT_NORMAL = "paas.udobj.public_object.company_relation_type_not_normal";
    String ENTERPRISE_RELATION_NOT_EXIT = "paas.udobj.public_object.enterprise_relation_not_exit";
    String NOT_ENABLED_PUBLIC_OBJECT = "paas.udobj.public_object.not_enabled_public_object";
    String INVITATION_PROCESSED = "paas.udobj.public_object.invitation_processed";
    String NOT_SUPPORT_SLAVE_OBJECT = "paas.udobj.public_object.not_support_slave_object";
    String NOT_SUPPORT_MASTER_OBJECT = "paas.udobj.public_object.not_support_master_object";
    String NOT_SUPPORT_MASTER_DETAIL = "paas.udobj.public_object.not_support_master_detail";
    String ENTERPRISE_NOT_HAVE_RELATION_OWNER = "paas.udobj.public_object.enterprise_not_have_relation_owner";
    String HAVE_UNFINISHED_JOB = "paas.udobj.public_object.have_unfinished_job";
    String PUBLIC_OBJECT_UN_SUPPORT_CUSTOM_FIELD = "paas.udobj.public_object.public_object_un_support_custom_field";
    String PUBLIC_OBJECT_OPEN = "paas.udobj.public_object.public_object_open";
    String PUBLIC_OBJECT_CLOSE = "paas.udobj.public_object.public_object_close";
    String PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT = "paas.udobj.public_object.public_object_upstream_tenant_object";
    String REJECT_INVITES_PUBLIC_OBJECT = "paas.udobj.public_object.reject_invites_public_object";
    String AGREE_INVITES_PUBLIC_OBJECT = "paas.udobj.public_object.agree_invites_public_object";
    String ENTERPRISE_OBJECT_NOT_EXIST = "paas.udobj.public_object.enterprise_object_not_exist";
    String PUBLIC_FIELD_PRIVATE_DATA_CANNOT_SET_REQUIRED = "paas.udobj.public_object.public_field_private_data_cannot_set_required";
    String PUBLIC_FIELD_CANNOT_SET_REQUIRED_OPTION_SET = "paas.udobj.public_object.public_field_cannot_set_required_option_set";
    String PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER = "paas.udobj.public_object.public_object_cannot_open_change_order";
    String NOT_SUPPORT_MODIFY_FIELD_DEPENDENCIES = "paas.udobj.public_object.not_support_modify_field_dependencies";
    String PUBLIC_OBJECT_ALL_ENTERPRISES = "paas.udobj.public_object.all_enterprises";

    String IMPORT_ADD_EXIST_DUPLICATE = "paas.udobj.import_add_exist_duplicate";
    String IMPORT_ADD_MATCH_RULE = "paas.udobj.import_add_match_rule";
    String IMPORT_ADD_NOT_ADD_MASTER_DETAIL = "paas.udobj.import_add_not_add_master_detail";
    String IMPORT_ADD_DATA_CORRECT = "paas.udobj.import_add_data_correct";

    // 解除绑定
    String UNBIND_ACCOUNT = "paas.udobj.action.unbind_account";
    //业务插件的依赖插件没有启用
    String DOMAIN_PLUGIN_DEPENDENCY_NOT_ENABLE = "paas.udobj.domain_plugin_dependency_not_enable";

    // "对象{0}数据{1}关联数据超过{2}条不能再关联，其他选择的数据已经关联成功。"
    String OBJECT_DATA_ASSOCIATED_OVER_LIMIT = "paas.udobj.object_data_associated_over_limit";
    // 活动举证
    String ACTIVITY_PROOF = "paas.udobj.action.activity.proof";
    //统计字段不支持使用最后修改时间字段作为汇总字段或过滤范围:{0}
    String LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_COUNT = "paas.udobj.last_modified_time_field_not_support_count";
    //引用字段字段不支持引用最后修改时间字段:{0}
    String LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_QUOTE = "paas.udobj.last_modified_time_field_not_support_quote";
    //计算公式中不支持使用最后修改时间字段:{0}
    String LAST_MODIFIED_TIME_FIELD_NOT_SUPPORT_FORMULA = "paas.udobj.last_modified_time_field_not_support_formula";
    String EXCHANGE_OUTBOUND = "paas.udobj.action.exchange_outbound";
    String RED_ACCOUNTS_RECEIVABLE_CREATE = "paas.udobj.action.red_accounts_receivable_create";

    //当前对象的变更单apiName已经被占用，无法开启变更单
    String CHANGE_ORDER_OBJECT_API_NAME_EXIST = "paas.udobj.change_order_api_name_exist";
    //已经开启变更单的对象，不能增加主从字段
    String CHANGE_ORDER_EXISTS_CANNOT_ADD_MD = "paas.udobj.change_order_exists_cannot_add_md";
    //变更规则中记录原值的字段超出限制，对象:{}
    String CHANGE_RULE_ORIGINAL_COUNT_OUT_OF_LIMIT = "paas.udobj.change_rule_original_count_out_of_limit";
    //从对象单独新建的不允许开启变更单
    String DETAIL_OBJ_SINGLE_CREATE_CANNOT_OPEN_CHANGE_ORDER = "paas.udobj.detail_obj_single_create_cannot_open_change_order";
    String ADJUST_AFTER_COUNTING = "paas.udobj.action.adjust_after_counting";
    String ADD_PURCHASE_GOODS_RECEIVED_NOTE = "paas.udobj.action.add_purchase_goods_received_note";
    String CLOSE_ACTIVITY_AGREEMENT = "paas.udobj.action.activity.agreement";
    String IMPORT_TEAM_MEMBER_NOT_EXIST = "paas.udobj.import_team_member_not_exist";
    String IMPORT_TEAM_MEMBER_NAME = "paas.udobj.import_team_member_name";
    //从对象:{},未配置变更规则，请联系管理员配置后变更
    String DETAIL_OBJ_NONE_CHANGE_RULE = "paas.udobj.detail_obj_none_change_rule";

    String CHANGE_ORDER_EXISTS_CANNOT_DELETE_MASTER_DETAIL_FIELD = "paas.udobj.change_order_exists_cannot_delete_master_detail_field";

    String FLOW_TASK_LIST_PAGE = "paas.udobj.flow_task_list_page";
    String FLOW_TASK_LIST_MOBILE_PAGE = "paas.udobj.flow_task_list_mobile_page";
    String DEFAULT_FLOW_TASK_LIST_LAYOUT = "paas.udobj.default_flow_task_list_layout";
    String DEFAULT_FLOW_WHAT_LAYOUT = "paas.udobj.default_what_list_layout";
    String UPDATE_RISK_INFORMATION = "paas.udobj.action.activity.update_risk_information";
    // 源对象或目标对象不存在或被禁用，对象apiName:{0}
    String SOURCE_OR_TARGET_OBJECT_UNEXIST_OR_DISABLED = "paas.udobj.source_or_target_object_unexist_or_disabled";

    String PRESETED_PLUGIN_NOT_DISABLED = "paas.udobj.preseted_plugins_not_allowed_be_disabled";
    String DOMAIN_PLUGIN_RECORD_TYPE_NOT_SAME = "paas.udobj.domain_plugin_record_type_not_same";
    String BLIACK_LIST = "paas.udobj.action.black_list";

    String OPEN_CHANGE_ORDER_OBJ_CANNOT_DISABLE_OR_DELETE = "paas.udobj.open_change_order_obj_cannot_disable_or_delete";
    String MEMBER_TYPE_EMPLOYEE = "paas.udobj.member_type_employee";
    String MEMBER_TYPE_DEPARTMENT = "paas.udobj.member_type_department";
    String MEMBER_TYPE_GROUP = "paas.udobj.member_type_group";
    String MEMBER_TYPE_ROLE = "paas.udobj.member_type_role";
    String MEMBER_TYPE_OUT_TENANT = "paas.udobj.member_out_tenant";
    String MEMBER_TYPE_OUT_TENANT_GROUP = "paas.udobj.member_out_tenant_group";
    String MEMBER_TYPE_INTERCONNECT_DEPARTMENT = "paas.udobj.member_type_interconnect_department";
    String PERMISSION_READONLY = "paas.udobj.permission_readonly";
    String PERMISSION_READANDWRITE = "paas.udobj.permission_readandwrite";
    String NO_PERMISSION = "paas.udobj.no_permission";

    String UN_SUPPORT_UPLOAD_FILE_ATTACHMENT_TYPE = "paas.udobj.un_support_upload_file_attachment_type";

    String BUTTON_PEER_DISPLAY_NAME = "paas.udobj.button_peer_display_name";
    String OUT_ENTERPRISE_OUT_EMPLOYEE = "paas.udobj.out_enterprise_out_employee";
    String OUT_ENTERPRISE_OUT_DEPARTMENT = "paas.udobj.out_enterprise_out_department";

    String FALLBACK = "paas.udobj.action.fallback";
    String CANCEL_LOYALTY = "paas.udobj.action.cancel_loyalty";
    String POOL_CHANGE_POINTS = "paas.udobj.action.pool_change_points";
    String MEMBER_CHANGE_POINTS = "paas.udobj.action.member_change_points";
    String MEMBER_TRANSFER_POINTS = "paas.udobj.action.member_transfer_points";
    String MEMBER_MERGE = "paas.udobj.action.member_merge";
    String MEMBER_SET_LEVEL = "paas.udobj.action.member_set_level";

    String MEMBER_WORKING_HOURS_FILL = "paas.udobj.action.member_working_hours_fill";
    String STOCK_QUANTITY_REFRESH = "paas.udobj.action.stock_quantity_refresh";
    String DEVICE_BOM_CONFIG = "paas.udobj.action.device_bom_config";
    String ASSET_BORROWING_RETURN = "paas.udobj.action.asset_borrowing_return";
    String ASSET_BORROWING_EXTENSION = "paas.udobj.action.asset_borrowing_extension";
    String FIELD_NOT_SUPPORT_FILTER = "paas.udobj.field_not_support_filter";
    String OCR_FIELD_MAPPING_ERROR = "paas.udobj.ocr_field_mapping_error";
    String OCR_BANKCARD_BANKCARDTYPE_0 = "paas.udobj.ocr_bankcard_bankcardtype_0";
    String OCR_BANKCARD_BANKCARDTYPE_1 = "paas.udobj.ocr_bankcard_bankcardtype_1";
    String OCR_BANKCARD_BANKCARDTYPE_2 = "paas.udobj.ocr_bankcard_bankcardtype_2";
    String OCR_BANKCARD_BANKCARDTYPE_3 = "paas.udobj.ocr_bankcard_bankcardtype_3";
    String OCR_BANKCARD_BANKCARDTYPE_4 = "paas.udobj.ocr_bankcard_bankcardtype_4";
    String OCR_MD_FIELD_ERROR = "paas.udobj.ocr_md_field_error";
    // 源单数据源单ID数量超过限制，最多支持{0}个
    String CONVERT_RULE_SOURCE_IDS_TOO_MUCH = "paas.udobj.convert_rule_source_ids_too_much";
    // 简单场景下，二次拉单时只能选择一条源单数据。
    String SIMPLE_SCENE_ONLY_SELECT_ONE_SOURCE_DATA = "paas.udobj.simple_scene_only_select_one_source_data";
    // 简单场景下，二次拉单时只能选择已经关联源单数据。
    String SIMPLE_SCENE_ONLY_SELECT_LOOKUP_SOURCE_DATA = "paas.udobj.simple_scene_only_select_lookup_source_data";
    // 转换规则场景类型错误
    String CONVERT_RULE_SCENE_TYPE_ERROR = "paas.udobj.convert_rule_scene_type_error";
    // 转换规则二次拉单不支持按规则分组
    String CONVERT_RULE_DOUBLE_PULL_NOT_SUPPORT_GROUPING_BY_RULE = "paas.udobj.convert_rule_double_pull_not_support_grouping_by_rule";
    // 拆单生成条数暂不能超过{0}条，请重新选取转换数据或联系系统管理员调整字段分组逻辑
    String CONVERT_RULE_SPLIT_ORDER_EXCEED_MAX_LIMIT = "paas.udobj.convert_rule_split_order_exceed_max_limit";

    String NUMBER_FIELD_STEP_MORE_LIMIT = "paas.udobj.number_field_step_more_limit";

    //在线文档
    //新建插件缺少defineType
    String ONLINE_DOC_ARG_ERROR_MISSING_DEFINE_TYPE = "paas.udobj.online_doc.arg_error.missing_define_type";
    //新建插件缺少name
    String ONLINE_DOC_ARG_ERROR_MISSING_NAME = "paas.udobj.online_doc.arg_error.missing_name";
    //新建插件缺少icon
    String ONLINE_DOC_ARG_ERROR_MISSING_ICON = "paas.udobj.online_doc.arg_error.missing_icon";
    //新建插件缺少appType
    String ONLINE_DOC_ARG_ERROR_MISSING_APP_TYPE = "paas.udobj.online_doc.arg_error.missing_app_type";
    //新建插件缺少插件apiName
    String ONLINE_DOC_ARG_ERROR_MISSING_PLUGIN_API_NAME = "paas.udobj.online_doc.arg_error.missing_plugin_api_name";
    //新建插件缺少函数apiName
    String ONLINE_DOC_ARG_ERROR_MISSING_FUNCTION_API_NAME = "paas.udobj.online_doc.arg_error.missing_function_api_name";
    //新建插件缺少扩展字段
    String ONLINE_DOC_ARG_ERROR_MISSING_EXTRA_INFO = "paas.udobj.online_doc.arg_error.missing_extra_info";
    //新建插件缺少自定义组件apiName
    String ONLINE_DOC_ARG_ERROR_MISSING_PWC_API_NAME = "paas.udobj.online_doc.arg_error.missing_pwc_api_name";
    //{0}字段内容过长
    String ONLINE_DOC_ARG_ERROR_CONTENT_TOO_LONG = "paas.udobj.online_doc.arg_error.content_too_long";
    //查询插件不存在，请安装后重试
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_MISSING_ENTITY = "paas.udobj.online_doc.plugin_entity_error.missing_entity";
    //插件缺少绑定信息
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_MISSING_BINDING = "paas.udobj.online_doc.plugin_entity_error.missing_binding";
    //插件禁止绑定
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_DISABLE_BINDING = "paas.udobj.online_doc.plugin_entity_error.disable_binding";
    //插件禁止解绑
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_DISABLE_UNBINDING = "paas.udobj.online_doc.plugin_entity_error.disable_unbinding";
    //插件状态不可用
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_NEED_ENABLE = "paas.udobj.online_doc.plugin_entity_error.need_enable";
    //请先禁用插件
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_NEED_DISABLE_FIRST = "paas.udobj.online_doc.plugin_entity_error.need_disable_first";
    //插件未绑定账号
    String ONLINE_DOC_PLUGIN_ENTITY_ERROR_NEED_BANDING = "paas.udobj.online_doc.plugin_entity_error.need_banding";
    //未找到授权记录
    String ONLINE_DOC_AUTH_ENTITY_ERROR_MISSING_ENTITY = "paas.udobj.online_doc.auth_entity_error.missing_entity";
    //函数执行异常:{0}
    String ONLINE_DOC_FUNCTION_ERROR = "paas.udobj.online_doc.function_error";
    String EXPORT_XML_MAX_LENGTH_ERROR = "paas.udobj.export_xml_max_length_error";
    String EXPORT_FILE_MAX_SIZE_ERROR = "paas.udobj.export_file_max_size_error";
    String EXPORT_EMBEDDED_EXCEL_IMAGE_MAX_SIZE_ERROR = "paas.udobj.export_embedded_excel_image_max_size_error";
    String IDENTIFY_ACCOUNT = "paas.udobj.action.identify_account";
    String CONFIRM_RECEIPT_PAYMENT = "paas.udobj.action.confirm_receipt_payment";
    String FREEZE_INVENTORY_ADJUSTMENT = "paas.udobj.action.freeze_inventory_adjustment";
    String OPTION_SET_VERSION_CONFLICT = "paas.udobj.option_set_version_conflict";
    String MULTI_LANG_FIELD_COUNT_ERROR = "paas.udobj.multi_lang_field_count_error";
    String INSPECTION_RECORD_OK = "paas.udobj.action.inspection_record_ok";
    String INSPECTION_RECORD_APPEAL = "paas.udobj.action.inspection_record_appeal";
    String CREATE_PARTNER = "paas.udobj.action.create_partner";
    String SYNC_TO_PRICE_BOOK = "paas.udobj.action.sync_to_price_book";
    // 点击了自定义按钮: {0}({1})
    String CUSTOM_BUTTON_CLICK = "paas.udobj.action.click_custom_button.new";
    String NO_SIDEBAR_LAYOUT_TEMPLATE = "paas.udobj.no_sidebar_layout_template";
    /**
     * 验证码：{}（请勿转告他人），有效期15分钟，请尽快完成验证。
     */
    String DEFAULT_SM_LOGIN_I18N_KEY = "register.default_sm_validate_code_content";
    String TASK_MEMBER_PUBLISH = "paas.udobj.action.task_member_publish";
    String RENEW_EXPIRATION = "paas.udobj.action.renew.expiration";
    String ACC_RENEW_EXPIRATION = "paas.udobj.action.acc.renew.expiration";

    String EDIT_PAGE_LABEL_SUFFIX = "paas.udobj.edit_page_label_suffix";
    String CONFIG_PRINT_TEMPLATE = "paas.udobj.action.config_print_template";
    String CONFIG_BPM = "paas.udobj.action.config_bpm";
    String CONFIRM_OUTBOUND = "paas.udobj.action.confirm_outbound";
    String COMPLETE = "paas.udobj.action.complete";
    String SETTLEMENTAR = "paas.udobj.action.settlement_ar";
    String TASK_CLOSE = "paas.udobj.action.task_close";
    String START_SETTLEMENT = "paas.udobj.action.start.settlement";

    String LIFE_STATUS_BEFORE_INVALID_LABEL = "paas.udobj.life_status_before_invalid_label";
    String HANDLE_SPECIAL_FIELD_TIMEOUT = "paas.udobj.handle_special_field_timeout";
    String UNSUPPORTED_CREATE_CHANGE_ORDER = "paas.udobj.unsupported_create_change_order";
    String CHANGE_ORDER_OBJECT_NOT_EXIST_OR_DELETED = "paas.udobj.change_order_object_not_exist_or_deleted";
    String GENERIC_BUTTON_ONLY_SUPPORTS_UIACTION = "paas.udobj.generic_button_only_supports_uiaction";
    String LIST_GENERIC_BUTTON_CANNOT_SET_BUTTON_PARAMETERS = "paas.udobj.list_generic_button_cannot_set_button_parameters";
    String LIST_GENERIC_BUTTON_CANNOT_SET_BUTTON_DISPLAY_CONDITIONS = "paas.udobj.list_generic_button_cannot_set_button_display_conditions";
    String ENABLE_GDPR_FAILURE_CONTACT_FENXIANG_CUSTOMER_SERVICE = "paas.udobj.enable_gdpr_failure_contact_fenxiang_customer_service";
    String DISABLE_GDPR_FAILURE_CONTACT_FENXIANG_CUSTOMER_SERVICE = "paas.udobj.disable_gdpr_failure_contact_fenxiang_customer_service";
    String CURRENT_CODE_HAS_NEWER_VERSION_ONLINE_PLEASE_PULL_BEFORE_UPLOADING = "paas.udobj.current_code_has_newer_version_online_please_pull_before_uploading";
    String XML_CONFIGURATION_FILE_ERROR = "paas.udobj.xml_configuration_file_error";
    String TAG_CANNOT_BE_EMPTY = "paas.udobj.tag_cannot_be_empty";
    String OBJECT_API_NAME_CANNOT_BE_EMPTY = "paas.udobj.object_api_name_cannot_be_empty";
    String CHANGE_ORDER = "paas.udobj.change_order";
    String FIELD_NOT_EXIST_OR_NOT_AUTO_INCREMENT = "paas.udobj.field_not_exist_or_not_auto_increment";
    String DUPLICATE_CHECK_SERVICE_EXCEPTION = "paas.udobj.duplicate_check_service_exception";
    String DEFAULT_DUPLICATE_CHECK_TOOL_RULES = "paas.udobj.default_duplicate_check_tool_rules";
    String DEFAULT_CLEANSING_RULES = "paas.udobj.default_cleansing_rules";
    String DEFAULT_NEW_DUPLICATE_CHECK_RULES = "paas.udobj.default_new_duplicate_check_rules";
    String ERROR_OBTAINING_NPATH = "paas.udobj.error_obtaining_npath";
    String SERVICE_INVOCATION_EXCEPTION = "paas.udobj.service_invocation_exception";
    String CURRENT_SCENARIO_CANNOT_BE_DELETED = "paas.udobj.current_scenario_cannot_be_deleted";
    String DEFAULT_GROUP = "paas.udobj.default_group";
    String VALIDATION_RULE_NOT_EXIST_OR_DELETED = "paas.udobj.validation_rule_not_exist_or_deleted";
    String DECODING_FAILURE = "paas.udobj.decoding_failure";
    String AFFECTED_BY_NETWORK_ANOMALIES = "paas.udobj.affected_by_network_anomalies";
    String EXPORT_FAILED_PLEASE_RETRY_LATER = "paas.udobj.export_failed_please_retry_later";
    String INSUFFICIENT_QUOTA = "paas.udobj.insufficient_quota";
    String action_share_friends = "paas.udobj.action.share_friends";
    String TASK_POSTER_CHECK = "paas.udobj.action.task_poster_check";
    String AUTO_MATCH = "paas.udobj.action.auto_match";
    String MANUAL_MATCH = "paas.udobj.action.manual_match";
    String MODIFY_SETTLEMENT_RULES = "paas.udobj.action.modify_settlement_rules";
    String INITIATE_RENEWAL = "paas.udobj.action.initiate_renewal";
    String APPROVAL_DELAY_TASK_EXECUTE = "paas.udobj.action.approval_delay_task_execute";
    String BPM_DELAY_TASK_EXECUTE = "paas.udobj.action.bpm_delay_task_execute";
    String JOB_STATUS_INIT = "paas.udobj.job.status.init";
    String JOB_STATUS_PROCESSING = "paas.udobj.job.status.processing";
    String JOB_STATUS_END = "paas.udobj.job.status.end";
    String JOB_STATUS_CANCELED = "paas.udobj.job.status.canceled";
    String JOB_STATUS_FAILED = "paas.udobj.job.status.failed";
    String ADD_TEAM_MEMBER_LIMIT = "paas.udobj.add_team_member_limit";
    String SUSPENDED_COMPONENT_ERROR = "paas.udobj.suspended_component_error";
    String SUBMIT_ACCOUNT = "paas.udobj.action.submit_account";
    String CLOSE_ACCOUNT = "paas.udobj.action.close_account";
    String SETTLE_ACCOUNT = "paas.udobj.action.settle_account";
    String REWARD_RECORD_PUBLISH = "paas.udobj.action.reward_record_publish";

    String TEXT_COMPONENT_CONTENT = "paas.udobj.layout.text_component_content";

    String VALIDATE_RULE_BLOCK_INFO = "paas.udobj.validate.validate_rule_block_info";
    String CONVERSION_RULES_LICENSE_NOT_PURCHASED_OR_EXPIRED = "paas.udobj.conversion_rules_license_not_purchased_or_expired";

    String CANCEL_BUDGET_PROVISION = "paas.udobj.action.cancel.budget.provision";
    String REUSE_PROVISION_OCCUPY = "paas.udobj.action.reuse.provision.occupy";
    // 异步计算[{0}]对象的字段apiName[{1}]
    String FORMULA_FIELD_ASYNC_TASK = "paas.udobj.formula_field_async_task";
    String HAS_BEEN_DELETED = "paas.udobj.action.has_been_deleted";

    String FAILED_TO_GET_DATA_PERMISSION = "paas.udobj.failed_to_get_data_permission";

    String UNABLE_TO_UPDATE_CHANGE_ORDER_DATA = "paas.udobj.unable_to_update_change_order_data.";
    // 组件之间或组件内字段apiName:{0}重复,请修正后重新保存。
    String GROUP_FIELD_DUPLICATED = "paas.udobj.group_field_duplicated";
    String BOM_CORE_CONFIG_OCR = "paas.udobj.action.bom_core_config_ocr";
    String APPLY_EXIST_ACCOUNTS = "paas.udobj.action.apply_exist_accounts";
    String REWARD_RECORD_DETAIL_PUBLISH = "paas.udobj.action.reward_record_detail_publish";

    String SET_GOAL = "paas.udobj.action.set_goal";

    String SYNC_TO_QW = "paas.udobj.action.sync_to_qw";
    String CREATE_SALE_OUTBOUND = "paas.udobj.action.create_sale_outbound";
    String VIEW_GOAL = "paas.udobj.action.view_goal";
    String RENEW = "paas.udobj.action.renew";

    String ON_SHELF = "paas.udobj.action.on.shelf";
    String OFF_SHELF = "paas.udobj.action.off.shelf";
    String ADD_NON_STANDARD_PRODUCT = "paas.udobj.action.add.non.standard.product";
    String TRANSFER_ORDER = "paas.udobj.transfer_order";
    String VIRTUAL_STOCK_IN = "paas.udobj.virtual_stock_in";

    String ADD_SUB_CONTRACT = "paas.udobj.add_sub_contract";

    String RECYCLE_BIN_USER_STOP = "paas.udobj.recycle_bin_user_stop";

    String ENABLE_ACCOUNT_CHECK_RULE = "paas.udobj.action.enable_account_check_rule";
    String DISABLE_ACCOUNT_CHECK_RULE = "paas.udobj.action.disable_account_check_rule";
    String RULE_NOT_FOUND = "paas.udobj.rule.not.found";
    String FREEZE_INVENTORY_BATCH_ADJUSTMENT = "paas.udobj.action.freeze_inventory_batch_adjustment";

    /**
     * Failed to query external employee information: {0}
     * 查询外部员工信息失败：{0}
     */
    String GET_OUTER_EMPLOYEE_ERROR = "paas.udobj.get_outer_employee_error";

    /**
     * Failed to query external department information: {0}
     * 查询外部部门信息失败：{0}
     */
    String GET_OUTER_DEPARTMENT_ERROR = "paas.udobj.get_outer_department_error";

    /**
     * Data conversion failed, please try again later
     * 数据转换失败，请稍后重试
     */
    String DATA_CONVERT_ERROR = "paas.udobj.data_convert_error";
    String BI_SERVICE_ERROR = "paas.udobj.bi_service_error";
    // 对象{0}缺失, 请刷新页面后重试
    String CALCULATE_OBJ_DESCRIBE_NULL = "paas.udobj.calculate_obj_describe_null";
    String CHANGE_ORDER_RULE_NOT_FOUND = "paas.udobj.change_order_rule_not_found";
    String CAN_NOT_START_CHANGE_ORDER = "paas.udobj.can_not_start_change_order";
    String CHANGE_ORDER_MASTER_DATA_NOT_EXIST_OR_DELETED = "paas.udobj.change_order_master_data_not_exist_or_deleted";

    /**
     * [{0}]协同富文本字段格式错误，请重新编辑内容或清除特殊格式后再提交
     */
    String INVALID_FORMAT_COOPERATIVE_RICH_TEXT_RETRY = "paas.udobj.invalid_format_cooperative_rich_text_retry";
    //该字段在查重规则中配置了范围查询，不能关闭附近查找
    String GEO_FIELD_REFERENCE_DUP_RULE_ERROR = "paas.udobj.geo_field_reference_dup_rule_error";
    //查重规则中配置范围查询的字段类型错误
    String DUPLICATE_FILED_TYPE_ERROR = "paas.udobj.duplicate_filed_type_error";
    //查重规则中配置的范围查询字段未开启附近查找能力
    String DUPLICATE_GEO_FIELD_TYPE_ERROR = "paas.udobj.duplicate_geo_filed_type_error";

    String PATH_TRAVERSAL_COUNT_EXCEEDS_LIMIT = "paas.udobj.path_traversal_count_exceeds_limit";
    /**
     * 聚合字段[{0}]必须是数字、金额、百分数等数字相关类型
     * Aggregate field [{0}] must be a number, currency, percentage or other numeric type
     */
    String AGGREGATE_FIELD_TYPE_ERROR = "aggregate.field.type.error";
    String REFERENCE_NOT_EXIST_OR_DELETED = "paas.udobj.reference_not_exist_or_deleted";

    String CREATE_EXTENSION = "paas.udobj.create_extension";
    String UPDATE_EXTENSION = "paas.udobj.update_extension";
    String DELETE_EXTENSION = "paas.udobj.delete_extension";
    String DISABLE_EXTENSION = "paas.udobj.disable_extension";
    String ENABLE_EXTENSION = "paas.udobj.enable_extension";
    // 扩展 {0} ({1})
    String OBJECT_EXTENSION = "paas.udobj.object_extension";
    // {0}({1}) 数据不正确, plugin_provider:{2}, methods:{3}, module_name:{4}, module_type:{5}
    String EXTENSION_DATA_ERROR = "paas.udobj.extension_data_error";
    // 当前企业已存在api为{0}的配置
    String EXTENSION_API_NAME_EXIST = "paas.udobj.extension_api_name_exist";
    // 当前对象已存在相同适配类型的数据
    String EXTENSION_MODULE_NAME_EXIST = "paas.udobj.extension_module_name_exist";
    // 不存在api为{0}的配置
    String EXTENSION_API_NAME_NOT_EXIST = "paas.udobj.extension_api_name_not_exist";
    // 启用状态的配置不可以删除
    String EXTENSION_ENABLE_STATUS_NOT_DELETE = "paas.udobj.extension_enable_status_not_delete";
    // 函数类型及适配类型不允许更新
    String EXTENSION_FUNCTION_TYPE_AND_MODULE_TYPE_NOT_UPDATE = "paas.udobj.extension_function_type_and_module_type_not_update";
    // 更新失败
    String UPDATE_FAILED = "paas.udobj.update_failed";

    String NATURAL_LANGUAGE_TO_SEARCHQUERY_FAIL = "paas.udobj.natural_language_to_searchquery_fail";

    String SUPPORT_OR_FILTER = "paas.udobj.support_or_filter";
    String FILTER_DISPLAY_OR = "paas.udobj.filter_display_or";
    String FILTER_DISPLAY_AND = "paas.udobj.filter_display_and";

    String EXPRESSION_EMPTY = "paas.udobj.expression_empty";
    String OPERATOR_NODE_NO_CHILDREN = "paas.udobj.operator_node_no_children";
    String OR_NODE_CHILDREN_MUST_BE_AND_OR_VALUE = "paas.udobj.or_node_children_must_be_and_or_value";
    String AND_NODE_CHILDREN_CANNOT_CONTAIN_OR = "paas.udobj.and_node_children_cannot_contain_or";
    String UNSUPPORTED_NODE_TYPE = "paas.udobj.unsupported_node_type";
    String EXPRESSION_ILLEGAL = "paas.udobj.expression_illegal";
    String EXPRESSION_NOT_MATCH_FILTER_GROUP_STRUCTURE = "paas.udobj.expression_not_match_filter_group_structure";
    String CONVERT_EXPRESSION_ERROR = "paas.udobj.convert_expression_error";
    String VALIDATE_EXPRESSION_ERROR = "paas.udobj.validate_expression_error";
    String EXPRESSION_STRUCTURE_NOT_SUPPORT_CONVERT_TO_FILTER_GROUP = "paas.udobj.expression_structure_not_support_convert_to_filter_group";
    String IMAGE_LOAD_FAILED = "paas.udobj.image_load_failed";

    // 新增的变更单校验相关Key
    String CHANGE_ORDER_LIFE_STATUS_INVALID = "paas.udobj.change_order_life_status_invalid";
    String CHANGE_ORDER_EFFECTIVE_STATUS_INVALID = "paas.udobj.change_order_effective_status_invalid";
    String PROPOSAL_AI_GENERATE =  "paas.udobj.proposal_ai_generate";

    String AR_QUICK_CREATE = "paas.udobj.ar_quick_create";
}