package com.facishare.paas.appframework.core.exception;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
public abstract class AppBizException extends AppBusinessException {
    private String[] supplements;

    public AppBizException(String message, int errorCode, String... supplements) {
        super(message, errorCode);
        this.supplements = supplements;
    }

    public AppBizException(Throwable cause, String message, int errorCode, String... supplements) {
        super(message, cause, () -> errorCode);
        this.supplements = supplements;
    }

    public String[] getSupplements() {
        return supplements;
    }

    public String getCepErrorCode() {
        return "s" + getErrorCode();
    }

    @Override
    public final boolean isSupportI18nCode() {
        return true;
    }

    public String getEncodeSupplement() {
        return ArrayUtils.isEmpty(getSupplements()) ? null : urlEncode(JSON.toJSONString(getSupplements()));
    }


    private String urlEncode(String raw) {
        try {
            return URLEncoder.encode(raw, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn("AppBizException message supplements encode error, supplement:{}", raw, e);
        }
        return raw;
    }


}
