package com.facishare.paas.appframework.core.model;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * Created by YuanXiaolong
 * 国际化资源
 */
public class MessageResource {

    /** 语言地区信息*/
    private Locale locale;
    /** 资源文件绑定*/
    private ResourceBundle resourceBundle;
    /** 语言常量*/
    private static final String ZH_CN = "zh_CN";
    private static final String ZH_TW = "zh_TW";
    private static final String EN = "en";
    /** 资源文件路径*/
    private static final String RESOURCE_PATH = "message";

    public MessageResource (String lang) {
        if (EN.equals(lang)) {
            locale = Locale.US;
        } else if (ZH_TW.equals(lang)) {
            locale = Locale.TRADITIONAL_CHINESE;
        } else {
            locale = Locale.SIMPLIFIED_CHINESE;
        }
        resourceBundle = ResourceBundle.getBundle(RESOURCE_PATH, locale);
    }

    /**
     * 返回key对应的文本，不处理占位符
     * @param key 语言资源的key
     * @return 文本
     */
    public String getString(String key) {
        return resourceBundle.getString(key);
    }

    /**
     * 返回key对象的文本，动态处理占位符
     * @param key 语言资源的key
     * @param words 替换占位符的文字
     * @return 文本
     */
    public String getString(String key, Object... words) {
        String str = resourceBundle.getString(key);
        return MessageFormat.format(str, words);
    }

}
