package com.facishare.paas.appframework.core.exception;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/4.
 */
public class ControllerClassLoadException extends APPException {
    public ControllerClassLoadException(String message) {
        super(message);
    }

    public ControllerClassLoadException(String message, Throwable cause) {
        super(message, cause);
    }

    public ControllerClassLoadException(SystemErrorCode errorCode) {
        super(errorCode);
    }

    public ControllerClassLoadException(SystemErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
}
