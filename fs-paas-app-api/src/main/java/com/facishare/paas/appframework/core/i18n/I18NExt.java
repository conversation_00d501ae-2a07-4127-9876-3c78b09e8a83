package com.facishare.paas.appframework.core.i18n;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.util.Lang;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * create by z<PERSON><PERSON> on 2019/07/30
 */
public class I18NExt {
    private static final String I18NKEY_OBJ_SET_EDIT_HEADER_LANG_PREFIX = "paas.transworkbench.baseinfo.lang.";

    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("metadata");
    private static final Logger log = LoggerFactory.getLogger(I18NExt.class);

    public static boolean isGrayI18n(String tenantId) {
        return gray.isAllow("i18n", tenantId);
    }

    public static String text(String key) {
        return getOrDefault(key, key);
    }

    public static String text(String key, Object... placeHolder) {
        return getOrDefault(key, key, placeHolder);
    }

    public static String getOrDefault(String key, String defaultValue) {
        if (Strings.isNullOrEmpty(key)) {
            return defaultValue;
        }
        try {
            String result = I18N.text(key);
            if (Strings.isNullOrEmpty(result)) {
                return defaultValue;
            }
            return result;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String getOrDefault(String key, String defaultValue, Object... placeHolder) {
        try {
            String result = I18N.text(key, placeHolder);
            if (Strings.isNullOrEmpty(result)) {
                return MessageFormat.format(defaultValue, placeHolder);
            }
            return result;
        } catch (Exception e) {
            return MessageFormat.format(defaultValue, placeHolder);
        }
    }

    public static Map<String, String> getAllLangText(String key) {
        Map<String, String> result = Maps.newHashMap();
        Localization localization = I18nClient.getInstance().get(key, I18N.getContext().getTenantId());
        if (Objects.isNull(localization)) {
            return result;
        }
        for (Lang lang : Lang.values()) {
            String text = localization.get(lang.getValue(), false);
            if (!Strings.isNullOrEmpty(text)) {
                result.put(lang.getValue(), text);
            }
        }
        return result;
    }

    /**
     * 获取当前语言环境对应的翻译值
     *
     * @param key
     * @return
     */
    public static String getOnlyText(String key) {
        Localization localization = getLocalization(key);
        if (Objects.isNull(localization)) {
            return null;
        }
        return localization.get(I18N.getContext().getLanguage(), null);
    }

    public static String getOnlyTextOrDefault(String key, String defaultValue) {
        String value = getOnlyText(key);
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

    public static Localization getLocalization(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return null;
        }
        return I18nClient.getInstance().get(key, I18N.getContext().getTenantId());
    }

    /**
     * 获取指定语言
     *
     * @param key
     * @param lang
     * @return
     */
    public static String getDesignatedLanguage(String key, String tenantId, String lang) {
        if (Strings.isNullOrEmpty(key) || Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(lang)) {
            return null;
        }
        Localization localization = I18nClient.getInstance().get(key, Long.parseLong(tenantId));
        if (Objects.isNull(localization)) {
            return null;
        }
        return localization.get(lang, false);
    }

    public static String getI18NKey(String I18NKey) {
        return "#I18N#" + I18NKey;
    }

    public static String getLangI18NKey(Lang lang) {
        return getLangI18NKey(lang.getValue());
    }

    public static String getLangI18NKey(String lang) {
        String formatLang = StringUtils.replaceEach(lang, new String[]{"-", "_"}, new String[]{"", ""});
        return I18NKEY_OBJ_SET_EDIT_HEADER_LANG_PREFIX + StringUtils.lowerCase(formatLang);
    }

    public static String getSwitchState(boolean switchState) {
        String switchStateKey = I18NKey.SWITCH_CLOSE;
        if (switchState) {
            switchStateKey = I18NKey.SWITCH_ENABLE;
        }
        return text(switchStateKey);
    }

    public static String getTransValueByKeyList(List<String> keyList) {
        return I18nClient.getInstance().getByOrder(keyList, I18N.getContext().getTenantId(), I18N.getContext().getLanguage());
    }

    public static Localization getAllTransValueByOrder(List<String> preKeyList, Integer tenantId) {
        Map<Byte, String> data = Maps.newHashMap();

        for (String key : preKeyList) {
            Localization localization = I18nClient.getInstance().get(key, tenantId);
            if (Objects.isNull(localization)) {
                continue;
            }
            localization.getData().forEach(data::putIfAbsent);
        }
        Localization localization = new Localization();
        localization.setData(data);
        return localization;
    }

    // 比较 targetString 和 preKeys 对应的 Localization 中的 value, 返回第一个包含的 Localization, 否则返回empty
    public static Optional<Localization> getLocalization(String targetString, String ei, List<String> preKeys) {
        if (!NumberUtils.isCreatable(ei)) {
            ei = "0";
        }
        long eiLong = Long.parseLong(ei);
        Map<String, Localization> localizationMap = Maps.newHashMap();

        try {
            localizationMap = I18nClient.getInstance().get(preKeys, eiLong);
        } catch (Exception e) {
            log.warn("get i18n error", e);
        }

        if (Objects.isNull(localizationMap)) {
            return Optional.empty();
        }
        for (Localization localization : localizationMap.values()) {
            if (Objects.nonNull(localization) && localization.getData().containsValue(targetString)) {
                return Optional.of(localization);
            }
        }
        return Optional.empty();
    }
}
