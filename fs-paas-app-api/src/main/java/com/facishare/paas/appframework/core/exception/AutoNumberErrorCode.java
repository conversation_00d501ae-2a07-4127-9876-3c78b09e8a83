package com.facishare.paas.appframework.core.exception;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/01/11
 */
public enum AutoNumberErrorCode implements ErrorCode {
    AUTO_NUMBER_PREFIX_EXCEPTION(201_111_061, I18N.text(I18NKey.AUTONUMBER_PREFIX_LENGTH_CHECK_FAILED)),
    AUTO_NUMBER_POSTFIX_EXCEPTION(201_111_062, I18N.text(I18NKey.AUTONUMBER_SUFFIX_LENGTH_CHECK_FAILED)),
    AUTO_NUMBER_SERIAL_NUMBER_EXCEPTION(201_111_063, I18N.text(I18NKey.AUTONUMBER_SERIAL_NUMBER_LENGTH_CHECK_FAILED)),
    AUTO_NUMBER_CONDITION_EXCEPTION(201_111_064, I18N.text(I18NKey.AUTONUMBER_CONDITION_CHECK_FAILED));

    private int code;
    private String message;

    AutoNumberErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
