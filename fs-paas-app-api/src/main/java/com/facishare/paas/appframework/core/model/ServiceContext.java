package com.facishare.paas.appframework.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

/**
 * Service context
 */
@Data
public class ServiceContext {

    private static final Joiner VERTICAL_JOINER = Joiner.on("|").useForNull("");

    @Delegate
    private RequestContext requestContext;
    private String serviceName;
    private String serviceMethod;

    @JsonCreator
    public ServiceContext(@JsonProperty("requestContext") RequestContext requestContext,
                          @JsonProperty("serviceName") String serviceName,
                          @JsonProperty("serviceMethod") String serviceMethod) {
        this.requestContext = requestContext;
        this.serviceName = serviceName;
        this.serviceMethod = serviceMethod;
    }

    @JsonIgnore
    public String getIdempotentKey() {
        if (StringUtils.isBlank(getPostId())) {
            return null;
        }
        User user = getUser();
        return VERTICAL_JOINER.join(getPostId(), user.getTenantId(), user.getUpstreamOwnerIdOrUserId(),
                user.getOutTenantId(), user.getOutUserId());
    }
}
