package com.facishare.paas.appframework.core.exception;

import java.util.Objects;

/**
 * App 系统异常根
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/16.
 */
public class APPException extends RuntimeException {
    /**
     * 记录于平台的错误码
     */
    private SystemErrorCode errorCode;
    /**
     * 替换占位符位置的信息
     */
    private String[] supplements;

    public APPException(SystemErrorCode errorCode, String... supplements) {
        this.errorCode = errorCode;
    }

    public APPException(SystemErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public APPException(SystemErrorCode errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

    public APPException(SystemErrorCode errorCode, Throwable cause, String... supplements) {
        super(errorCode.getCode(), cause);
        this.errorCode = errorCode;
        this.supplements = supplements;
    }

    public APPException(String message) {
        super(message);
        this.errorCode = SystemErrorCode.METADATA_ERROR;
    }

    public APPException(String message, Throwable throwable) {
        super(message, throwable);
        this.errorCode = SystemErrorCode.METADATA_ERROR;
    }

    public SystemErrorCode getErrorCode() {
        return errorCode;
    }

    public String[] getSupplements() {
        return supplements;
    }

    public String getErrorMessage() {
        if(Objects.nonNull(errorCode)) {
            return errorCode.getDescription();
        }
        return "AppException";
    }
}
