package com.facishare.paas.appframework.core.model;

public class MessageResourceManager {

    private static final InheritableThreadLocal<MessageResource> THREAD_LOCAL = new InheritableThreadLocal<>();

    public static MessageResource getMessageResource() {
        return THREAD_LOCAL.get();
    }

    public static void setMessageResource(MessageResource messageResource) {
        THREAD_LOCAL.set(messageResource);
    }

    public static void removeResource() {
        THREAD_LOCAL.remove();
    }
}
