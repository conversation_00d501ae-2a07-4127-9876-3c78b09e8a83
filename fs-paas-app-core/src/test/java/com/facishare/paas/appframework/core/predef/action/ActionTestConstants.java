package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.util.Set;

/**
 * GenerateByAI
 * Action测试专用的常量类
 * 
 * 功能特性：
 * - 定义Action测试的标准常量
 * - 提供统一的测试数据配置
 * - 遵循框架标准示例的常量定义
 * - 支持不同测试场景的常量需求
 * 
 * 设计原则：
 * - 常量类模式，只包含静态final字段
 * - 按功能分组组织常量
 * - 提供清晰的常量命名
 * - 支持测试数据的标准化
 */
public final class ActionTestConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private ActionTestConstants() {
        throw new UnsupportedOperationException("常量类不能被实例化");
    }

    // ==================== 基础测试常量 ====================

    /**
     * 标准测试租户ID
     * 遵循框架标准示例：'74255'
     */
    public static final String STANDARD_TENANT_ID = "74255";

    /**
     * 标准测试用户ID
     */
    public static final String STANDARD_USER_ID = "1000";

    /**
     * 备用测试租户ID
     */
    public static final String ALTERNATIVE_TENANT_ID = "78057";

    /**
     * 备用测试用户ID
     */
    public static final String ALTERNATIVE_USER_ID = "2000";

    /**
     * 系统用户ID
     */
    public static final String SYSTEM_USER_ID = "system";

    // ==================== 对象和字段常量 ====================

    /**
     * 标准测试对象API名称
     */
    public static final String STANDARD_OBJECT_API_NAME = "TestObj__c";

    /**
     * 标准测试对象显示名称
     */
    public static final String STANDARD_OBJECT_DISPLAY_NAME = "测试对象";

    /**
     * 主对象API名称
     */
    public static final String MASTER_OBJECT_API_NAME = "master__c";

    /**
     * 详情对象API名称
     */
    public static final String DETAIL_OBJECT_API_NAME = "detail__c";

    /**
     * 标准字段API名称
     */
    public static final String STANDARD_FIELD_API_NAME = "test_field";

    /**
     * 名称字段API名称
     */
    public static final String NAME_FIELD_API_NAME = "name";

    /**
     * 所有者字段API名称
     */
    public static final String OWNER_FIELD_API_NAME = "owner";

    /**
     * 状态字段API名称
     */
    public static final String STATUS_FIELD_API_NAME = "status";

    /**
     * ID字段API名称
     */
    public static final String ID_FIELD_API_NAME = "_id";

    // ==================== 字段类型常量 ====================

    /**
     * 文本字段类型
     */
    public static final String TEXT_FIELD_TYPE = IFieldType.TEXT;

    /**
     * 员工字段类型
     */
    public static final String EMPLOYEE_FIELD_TYPE = IFieldType.EMPLOYEE;

    /**
     * 单选字段类型
     */
    public static final String SELECT_ONE_FIELD_TYPE = IFieldType.SELECT_ONE;

    /**
     * 多选字段类型
     */
    public static final String SELECT_MANY_FIELD_TYPE = IFieldType.SELECT_MANY;

    /**
     * 数字字段类型
     */
    public static final String NUMBER_FIELD_TYPE = IFieldType.NUMBER;

    /**
     * 日期字段类型
     */
    public static final String DATE_FIELD_TYPE = IFieldType.DATE;

    /**
     * 日期时间字段类型
     */
    public static final String DATETIME_FIELD_TYPE = IFieldType.DATETIME;

    // ==================== 测试数据常量 ====================

    /**
     * 标准测试对象ID
     */
    public static final String STANDARD_TEST_ID = "test_id_001";

    /**
     * 标准测试对象名称
     */
    public static final String STANDARD_TEST_NAME = "测试对象";

    /**
     * 标准测试标签
     */
    public static final String STANDARD_TEST_LABEL = "测试标签";

    /**
     * 标准测试描述
     */
    public static final String STANDARD_TEST_DESCRIPTION = "这是一个测试描述";

    /**
     * 标准测试金额
     */
    public static final String STANDARD_TEST_AMOUNT = "1000.00";

    /**
     * 标准测试货币
     */
    public static final String STANDARD_TEST_CURRENCY = "CNY";

    // ==================== 状态常量 ====================

    /**
     * 激活状态
     */
    public static final String STATUS_ACTIVE = "1";

    /**
     * 非激活状态
     */
    public static final String STATUS_INACTIVE = "0";

    /**
     * 草稿状态
     */
    public static final String STATUS_DRAFT = "draft";

    /**
     * 已发布状态
     */
    public static final String STATUS_PUBLISHED = "published";

    /**
     * 已删除状态
     */
    public static final String STATUS_DELETED = "deleted";

    // ==================== 请求来源常量 ====================

    /**
     * CEP请求来源
     */
    public static final RequestContext.RequestSource REQUEST_SOURCE_CEP = RequestContext.RequestSource.CEP;

    /**
     * API请求来源
     */
    public static final RequestContext.RequestSource REQUEST_SOURCE_API = RequestContext.RequestSource.API;

    /**
     * 移动端请求来源
     */
    public static final RequestContext.RequestSource REQUEST_SOURCE_MOBILE = RequestContext.RequestSource.MOBILE;

    // ==================== 配置常量 ====================

    /**
     * 最大查询限制
     */
    public static final Integer MAX_QUERY_LIMIT = 1000;

    /**
     * 默认页面大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;

    /**
     * 最大页面大小
     */
    public static final Integer MAX_PAGE_SIZE = 200;

    /**
     * 移动端支持UI PaaS按钮的灰度企业ID集合
     */
    public static final Set<String> MOBILE_SUPPORT_UI_PAAS_BUTTON_GRAY_EI = Set.of("74255", "78057");

    // ==================== JSON模板常量 ====================

    /**
     * 标准测试数据JSON模板
     */
    public static final String STANDARD_TEST_DATA_JSON = 
        "{\"_id\":\"" + STANDARD_TEST_ID + "\"," +
        "\"name\":\"" + STANDARD_TEST_NAME + "\"," +
        "\"owner\":\"" + STANDARD_USER_ID + "\"," +
        "\"object_describe_api_name\":\"" + STANDARD_OBJECT_API_NAME + "\"}";

    /**
     * 主对象测试数据JSON模板
     */
    public static final String MASTER_TEST_DATA_JSON = 
        "{\"_id\":\"master_001\"," +
        "\"name\":\"主对象测试\"," +
        "\"owner\":\"" + STANDARD_USER_ID + "\"," +
        "\"mc_currency\":\"" + STANDARD_TEST_CURRENCY + "\"," +
        "\"object_describe_api_name\":\"" + MASTER_OBJECT_API_NAME + "\"}";

    /**
     * 详情对象测试数据JSON模板
     */
    public static final String DETAIL_TEST_DATA_JSON = 
        "{\"_id\":\"detail_001\"," +
        "\"name\":\"详情对象测试\"," +
        "\"master_id\":\"master_001\"," +
        "\"object_describe_api_name\":\"" + DETAIL_OBJECT_API_NAME + "\"}";

    /**
     * Wheres查询条件JSON模板
     */
    public static final String STANDARD_WHERES_JSON = 
        "{\"filters\":[{\"value_type\":9,\"operator\":\"EQ\"," +
        "\"field_name\":\"" + STANDARD_FIELD_API_NAME + "\"," +
        "\"field_values\":[\"test_function_api\"]}],\"connector\":\"AND\"}";

    /**
     * Filter过滤条件JSON模板
     */
    public static final String STANDARD_FILTER_JSON = 
        "{\"field_name\":\"" + STATUS_FIELD_API_NAME + "\"," +
        "\"operator\":\"EQ\",\"field_values\":[\"" + STATUS_ACTIVE + "\"],\"value_type\":0}";

    // ==================== 错误消息常量 ====================

    /**
     * 参数为空错误消息
     */
    public static final String ERROR_PARAMETER_NULL = "参数不能为空";

    /**
     * 对象不存在错误消息
     */
    public static final String ERROR_OBJECT_NOT_FOUND = "对象不存在";

    /**
     * 权限不足错误消息
     */
    public static final String ERROR_PERMISSION_DENIED = "权限不足";

    /**
     * 数据验证失败错误消息
     */
    public static final String ERROR_VALIDATION_FAILED = "数据验证失败";

    /**
     * 系统错误消息
     */
    public static final String ERROR_SYSTEM_ERROR = "系统错误";

    // ==================== 服务名称常量 ====================

    /**
     * Action测试服务名称
     */
    public static final String ACTION_TEST_SERVICE_NAME = "ActionTestService";

    /**
     * 缓存验证服务名称
     */
    public static final String CACHE_VALIDATE_SERVICE_NAME = "cacheValidate";

    /**
     * 数据导入服务名称
     */
    public static final String IMPORT_DATA_SERVICE_NAME = "importDataService";

    /**
     * 数据导出服务名称
     */
    public static final String EXPORT_DATA_SERVICE_NAME = "exportDataService";

    // ==================== 方法名称常量 ====================

    /**
     * 测试方法名称
     */
    public static final String TEST_METHOD_NAME = "test";

    /**
     * 保存方法名称
     */
    public static final String SAVE_METHOD_NAME = "save";

    /**
     * 删除方法名称
     */
    public static final String DELETE_METHOD_NAME = "delete";

    /**
     * 查询方法名称
     */
    public static final String QUERY_METHOD_NAME = "query";

    /**
     * 验证方法名称
     */
    public static final String VALIDATE_METHOD_NAME = "validate";
}
