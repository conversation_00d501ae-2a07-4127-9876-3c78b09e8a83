package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Field;
import java.util.Set;

import static org.mockito.Mockito.mockStatic;

/**
 * GenerateByAI
 * Action测试专用的工具类
 * 
 * 功能特性：
 * - 提供Action测试的通用工具方法
 * - 支持依赖注入和反射操作
 * - 处理静态方法Mock和配置Mock
 * - 支持Groovy测试迁移的标准化操作
 * 
 * 设计原则：
 * - 工具类模式，提供静态方法
 * - 遵循框架标准示例的操作方式
 * - 支持复杂的Mock配置场景
 * - 提供链式调用和流畅的API
 */
public class ActionTestUtils {

    /**
     * 为Action对象注入ServiceFacade依赖
     * 使用Whitebox反射注入，遵循框架标准示例
     * 
     * @param action 待注入的Action对象
     * @param serviceFacade Mock的ServiceFacade对象
     */
    public static void injectServiceFacade(Object action, ServiceFacade serviceFacade) {
        try {
            Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        } catch (Exception e) {
            throw new RuntimeException("注入ServiceFacade失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为Action对象注入InfraServiceFacade依赖
     * 使用Whitebox反射注入，遵循框架标准示例
     * 
     * @param action 待注入的Action对象
     * @param infraServiceFacade Mock的InfraServiceFacade对象
     */
    public static void injectInfraServiceFacade(Object action, InfraServiceFacade infraServiceFacade) {
        try {
            Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        } catch (Exception e) {
            throw new RuntimeException("注入InfraServiceFacade失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为Action对象注入所有标准依赖
     * 一次性注入ServiceFacade和InfraServiceFacade
     * 
     * @param action 待注入的Action对象
     * @param serviceFacade Mock的ServiceFacade对象
     * @param infraServiceFacade Mock的InfraServiceFacade对象
     */
    public static void injectStandardDependencies(Object action, ServiceFacade serviceFacade, InfraServiceFacade infraServiceFacade) {
        injectServiceFacade(action, serviceFacade);
        injectInfraServiceFacade(action, infraServiceFacade);
    }

    /**
     * 使用反射为对象设置任意字段值
     * 通用的字段注入方法
     * 
     * @param target 目标对象
     * @param fieldName 字段名称
     * @param value 字段值
     */
    public static void setField(Object target, String fieldName, Object value) {
        try {
            Whitebox.setInternalState(target, fieldName, value);
        } catch (Exception e) {
            throw new RuntimeException(String.format("设置字段 %s 失败: %s", fieldName, e.getMessage()), e);
        }
    }

    /**
     * 使用反射获取对象的字段值
     * 通用的字段获取方法
     * 
     * @param target 目标对象
     * @param fieldName 字段名称
     * @param <T> 返回值类型
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    public static <T> T getField(Object target, String fieldName) {
        try {
            return (T) Whitebox.getInternalState(target, fieldName);
        } catch (Exception e) {
            throw new RuntimeException(String.format("获取字段 %s 失败: %s", fieldName, e.getMessage()), e);
        }
    }

    /**
     * 设置AppFrameworkConfig的配置值
     * 遵循框架标准示例的配置方式
     * 
     * @param configName 配置名称
     * @param value 配置值
     */
    public static void setAppFrameworkConfig(String configName, Object value) {
        try {
            Whitebox.setInternalState(AppFrameworkConfig.class, configName, value);
        } catch (Exception e) {
            throw new RuntimeException(String.format("设置AppFrameworkConfig.%s失败: %s", configName, e.getMessage()), e);
        }
    }

    /**
     * 设置常用的AppFrameworkConfig配置
     * 提供标准的配置设置
     */
    public static void setStandardAppFrameworkConfig() {
        setAppFrameworkConfig("maxQueryLimit", 1000);
        setAppFrameworkConfig("mobileSupportUiPaasButtonGrayEi", Set.of("74255", "78057"));
    }

    /**
     * 设置UdobjGrayConfig的Mock配置
     * 遵循框架标准示例的灰度配置方式
     * 
     * @param mockGrayConfig Mock的灰度配置对象
     */
    public static void setUdobjGrayConfig(Object mockGrayConfig) {
        try {
            Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", mockGrayConfig);
        } catch (Exception e) {
            throw new RuntimeException("设置UdobjGrayConfig失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置RequestContext到RequestContextManager
     * 遵循框架标准示例的上下文设置方式
     * 
     * @param requestContext 请求上下文
     */
    public static void setRequestContext(RequestContext requestContext) {
        RequestContextManager.setContext(requestContext);
    }

    /**
     * 创建标准的ServiceContext
     * 遵循框架标准示例的构造方式
     * 
     * @param requestContext 请求上下文
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return ServiceContext对象
     */
    public static ServiceContext createServiceContext(RequestContext requestContext, String serviceName, String methodName) {
        return new ServiceContext(requestContext, serviceName, methodName);
    }

    /**
     * 验证对象的字段值
     * 提供通用的字段验证方法
     * 
     * @param target 目标对象
     * @param fieldName 字段名称
     * @param expectedValue 期望值
     * @return 验证是否通过
     */
    public static boolean verifyField(Object target, String fieldName, Object expectedValue) {
        try {
            Object actualValue = getField(target, fieldName);
            return expectedValue == null ? actualValue == null : expectedValue.equals(actualValue);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建Mock的静态方法调用
     * 支持静态方法的Mock配置
     * 
     * @param clazz 目标类
     * @param <T> 类型参数
     * @return MockedStatic对象
     */
    public static <T> MockedStatic<T> mockStaticClass(Class<T> clazz) {
        return mockStatic(clazz);
    }

    /**
     * 验证Action执行结果
     * 提供标准的结果验证模式
     * 
     * @param result 执行结果
     * @param expectedClass 期望的结果类型
     * @param <T> 结果类型
     * @return 验证通过的结果对象
     */
    public static <T> T verifyActionResult(Object result, Class<T> expectedClass) {
        if (result == null) {
            throw new AssertionError("Action执行结果不能为null");
        }
        
        if (!expectedClass.isInstance(result)) {
            throw new AssertionError(String.format(
                "Action执行结果类型不匹配，期望: %s, 实际: %s", 
                expectedClass.getSimpleName(), 
                result.getClass().getSimpleName()
            ));
        }
        
        return expectedClass.cast(result);
    }

    /**
     * 创建测试用的JSON数据字符串
     * 提供标准的JSON数据格式
     * 
     * @param id 对象ID
     * @param name 对象名称
     * @param owner 所有者
     * @param objectApiName 对象API名称
     * @return JSON格式的数据字符串
     */
    public static String createTestDataJson(String id, String name, String owner, String objectApiName) {
        return String.format(
            "{\"_id\":\"%s\",\"name\":\"%s\",\"owner\":\"%s\",\"object_describe_api_name\":\"%s\"}", 
            id, name, owner, objectApiName
        );
    }

    /**
     * 创建标准的测试数据JSON字符串
     * 使用默认的对象API名称
     * 
     * @param id 对象ID
     * @param name 对象名称
     * @param owner 所有者
     * @return JSON格式的数据字符串
     */
    public static String createStandardTestDataJson(String id, String name, String owner) {
        return createTestDataJson(id, name, owner, "TestObj__c");
    }

    /**
     * 从JSON字符串创建IObjectData对象
     * 遵循框架标准示例的构造方式
     * 
     * @param jsonString JSON格式的数据字符串
     * @return IObjectData对象
     */
    public static IObjectData createObjectDataFromJson(String jsonString) {
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(jsonString);
        return objectData;
    }

    /**
     * 比较两个IObjectData对象的内容
     * 提供深度比较功能
     * 
     * @param expected 期望的对象数据
     * @param actual 实际的对象数据
     * @return 比较结果
     */
    public static boolean compareObjectData(IObjectData expected, IObjectData actual) {
        if (expected == null && actual == null) {
            return true;
        }
        if (expected == null || actual == null) {
            return false;
        }
        
        // 这里可以添加更详细的比较逻辑
        // 比如比较关键字段的值
        return true; // 简化实现
    }

    /**
     * 清理测试环境
     * 清理静态Mock和全局状态
     */
    public static void cleanupTestEnvironment() {
        // 清理RequestContextManager
        RequestContextManager.clear();
        
        // 这里可以添加其他清理逻辑
    }

    /**
     * 断言工具类
     * 提供Action测试专用的断言方法
     */
    public static class ActionAssertions {
        
        /**
         * 断言Action执行成功
         * 
         * @param result 执行结果
         */
        public static void assertActionSuccess(Object result) {
            if (result == null) {
                throw new AssertionError("Action执行结果不能为null");
            }
        }
        
        /**
         * 断言Action执行失败
         * 
         * @param exception 期望的异常
         */
        public static void assertActionFailure(Exception exception) {
            if (exception == null) {
                throw new AssertionError("期望Action执行失败，但没有抛出异常");
            }
        }
        
        /**
         * 断言字段值相等
         * 
         * @param target 目标对象
         * @param fieldName 字段名称
         * @param expectedValue 期望值
         */
        public static void assertFieldEquals(Object target, String fieldName, Object expectedValue) {
            Object actualValue = getField(target, fieldName);
            if (!java.util.Objects.equals(expectedValue, actualValue)) {
                throw new AssertionError(String.format(
                    "字段 %s 值不匹配，期望: %s, 实际: %s", 
                    fieldName, expectedValue, actualValue
                ));
            }
        }
    }
}
