package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * BaseActionTest基类的单元测试
 * 验证Action测试基础设施的功能正确性
 */
@ExtendWith(MockitoExtension.class)
class BaseActionTestTest {

    /**
     * 测试用的BaseActionTest实现类
     */
    private static class TestableBaseActionTest extends BaseActionTest {
        // 测试专用的实现类，用于验证抽象基类的功能
    }

    private TestableBaseActionTest baseActionTest;

    @BeforeEach
    void setUp() {
        baseActionTest = new TestableBaseActionTest();
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证BaseActionTest的基本初始化功能
     */
    @Test
    @DisplayName("BaseActionTest基本初始化测试")
    void testBaseInitialization() {
        // Arrange & Act: 初始化已在setUp中完成

        // Assert: 验证基本属性已正确初始化
        assertNotNull(baseActionTest.serviceFacade, "ServiceFacade应该被正确初始化");
        assertNotNull(baseActionTest.infraServiceFacade, "InfraServiceFacade应该被正确初始化");
        assertNotNull(baseActionTest.mockObjectDescribe, "MockObjectDescribe应该被正确初始化");
        assertNotNull(baseActionTest.testObjectData, "TestObjectData应该被正确初始化");
        assertNotNull(baseActionTest.testUser, "TestUser应该被正确初始化");
        assertNotNull(baseActionTest.requestContext, "RequestContext应该被正确初始化");
        assertNotNull(baseActionTest.serviceContext, "ServiceContext应该被正确初始化");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证测试数据的正确性
     */
    @Test
    @DisplayName("测试数据验证")
    void testTestDataValidation() {
        // Arrange & Act: 获取测试数据
        IObjectData testData = baseActionTest.testObjectData;
        User testUser = baseActionTest.testUser;
        RequestContext requestContext = baseActionTest.requestContext;

        // Assert: 验证测试数据的正确性
        assertEquals("test_id_001", testData.get("_id"), "测试对象ID应该正确");
        assertEquals("测试对象", testData.get("name"), "测试对象名称应该正确");
        assertEquals("1000", testData.get("owner"), "测试对象所有者应该正确");
        assertEquals("TestObj__c", testData.get("object_describe_api_name"), "对象API名称应该正确");

        assertEquals("74255", testUser.getTenantId(), "用户租户ID应该正确");
        assertEquals("1000", testUser.getUserId(), "用户ID应该正确");

        assertEquals("74255", requestContext.getTenantId(), "请求上下文租户ID应该正确");
        assertEquals(testUser, requestContext.getUser(), "请求上下文用户应该正确");
        assertEquals(RequestContext.RequestSource.CEP, requestContext.getRequestSource(), "请求来源应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证createObjectData方法的功能
     */
    @Test
    @DisplayName("createObjectData方法测试")
    void testCreateObjectData() {
        // Arrange: 准备JSON数据
        String testJson = "{\"_id\":\"test123\",\"name\":\"测试\",\"owner\":\"user001\"}";

        // Act: 调用方法
        IObjectData result = baseActionTest.createObjectData(testJson);

        // Assert: 验证结果
        assertNotNull(result, "创建的对象数据不应为null");
        assertEquals("test123", result.get("_id"), "对象ID应该正确");
        assertEquals("测试", result.get("name"), "对象名称应该正确");
        assertEquals("user001", result.get("owner"), "对象所有者应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证createSystemUser方法的功能
     */
    @Test
    @DisplayName("createSystemUser方法测试")
    void testCreateSystemUser() {
        // Act: 调用方法
        User systemUser = baseActionTest.createSystemUser();

        // Assert: 验证结果
        assertNotNull(systemUser, "系统用户不应为null");
        assertEquals("74255", systemUser.getTenantId(), "系统用户租户ID应该正确");
        assertTrue(systemUser.isSystemUser(), "应该是系统用户");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证createStandardRequestContext方法的功能
     */
    @Test
    @DisplayName("createStandardRequestContext方法测试")
    void testCreateStandardRequestContext() {
        // Arrange: 准备用户对象
        User testUser = new User("12345", "test_user");

        // Act: 调用方法
        RequestContext result = baseActionTest.createStandardRequestContext(testUser);

        // Assert: 验证结果
        assertNotNull(result, "RequestContext不应为null");
        assertEquals("12345", result.getTenantId(), "租户ID应该正确");
        assertEquals(testUser, result.getUser(), "用户应该正确");
        assertEquals(RequestContext.RequestSource.CEP, result.getRequestSource(), "请求来源应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证createStandardServiceContext方法的功能
     */
    @Test
    @DisplayName("createStandardServiceContext方法测试")
    void testCreateStandardServiceContext() {
        // Arrange: 准备参数
        RequestContext requestContext = baseActionTest.requestContext;
        String serviceName = "TestService";
        String methodName = "testMethod";

        // Act: 调用方法
        ServiceContext result = baseActionTest.createStandardServiceContext(requestContext, serviceName, methodName);

        // Assert: 验证结果
        assertNotNull(result, "ServiceContext不应为null");
        assertEquals(requestContext, result.getRequestContext(), "RequestContext应该正确");
        assertEquals(serviceName, result.getServiceName(), "服务名称应该正确");
        assertEquals(methodName, result.getMethodName(), "方法名称应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证injectMockDependencies方法的功能
     */
    @Test
    @DisplayName("injectMockDependencies方法测试")
    void testInjectMockDependencies() {
        // Arrange: 创建测试Action对象
        TestAction testAction = new TestAction();

        // Act: 调用依赖注入方法
        assertDoesNotThrow(() -> {
            baseActionTest.injectMockDependencies(testAction);
        }, "依赖注入不应抛出异常");

        // Assert: 验证注入结果
        // 注意：由于使用了反射注入，这里主要验证不抛出异常
        // 具体的注入验证在ActionTestUtils的测试中进行
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证verifyActionResult方法的功能
     */
    @Test
    @DisplayName("verifyActionResult方法测试")
    void testVerifyActionResult() {
        // Arrange: 准备测试数据
        String testResult = "测试结果";
        Integer numberResult = 123;

        // Act & Assert: 验证正确的类型转换
        String stringResult = baseActionTest.verifyActionResult(testResult, String.class);
        assertEquals("测试结果", stringResult, "字符串结果应该正确");

        Integer intResult = baseActionTest.verifyActionResult(numberResult, Integer.class);
        assertEquals(123, intResult, "整数结果应该正确");

        // Act & Assert: 验证null值处理
        AssertionError nullException = assertThrows(AssertionError.class, () -> {
            baseActionTest.verifyActionResult(null, String.class);
        }, "null值应该抛出异常");
        assertTrue(nullException.getMessage().contains("不能为null"), "异常消息应该包含null提示");

        // Act & Assert: 验证类型不匹配处理
        AssertionError typeException = assertThrows(AssertionError.class, () -> {
            baseActionTest.verifyActionResult(testResult, Integer.class);
        }, "类型不匹配应该抛出异常");
        assertTrue(typeException.getMessage().contains("类型不匹配"), "异常消息应该包含类型不匹配提示");
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证createTestDataJson方法的功能
     */
    @Test
    @DisplayName("createTestDataJson方法测试")
    void testCreateTestDataJson() {
        // Arrange: 准备参数
        String id = "test123";
        String name = "测试对象";
        String owner = "user001";

        // Act: 调用方法
        String result = baseActionTest.createTestDataJson(id, name, owner);

        // Assert: 验证结果
        assertNotNull(result, "JSON字符串不应为null");
        assertTrue(result.contains("\"_id\":\"test123\""), "应该包含正确的ID");
        assertTrue(result.contains("\"name\":\"测试对象\""), "应该包含正确的名称");
        assertTrue(result.contains("\"owner\":\"user001\""), "应该包含正确的所有者");
        assertTrue(result.contains("\"object_describe_api_name\":\"TestObj__c\""), "应该包含正确的对象API名称");
    }

    /**
     * 测试用的Action类
     */
    private static class TestAction {
        private ServiceFacade serviceFacade;
        private InfraServiceFacade infraServiceFacade;

        // getter和setter方法用于测试依赖注入
        public ServiceFacade getServiceFacade() {
            return serviceFacade;
        }

        public void setServiceFacade(ServiceFacade serviceFacade) {
            this.serviceFacade = serviceFacade;
        }

        public InfraServiceFacade getInfraServiceFacade() {
            return infraServiceFacade;
        }

        public void setInfraServiceFacade(InfraServiceFacade infraServiceFacade) {
            this.infraServiceFacade = infraServiceFacade;
        }
    }
}
