package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.BaseServiceTest;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * GenerateByAI
 * Action类单元测试的统一基类，提供Action测试的通用配置和Mock对象
 * 
 * 功能特性：
 * - 继承BaseServiceTest的通用测试功能
 * - 提供Action类专用的Mock对象配置
 * - 统一的测试数据构造方法
 * - 支持Groovy测试迁移的标准化模式
 * - 严格遵循JUnit 5快速通道流程
 * 
 * 使用方式：
 * - Action测试类继承此基类
 * - 自动获得ServiceFacade、InfraServiceFacade等核心Mock对象
 * - 使用提供的工具方法构造测试数据
 * 
 * 设计原则：
 * - 遵循框架标准示例中的对象构造规范
 * - 支持super调用、静态方法调用的特殊处理
 * - 提供Groovy测试迁移的标准化支持
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public abstract class BaseActionTest extends BaseServiceTest {

    /**
     * 核心服务门面Mock对象
     * Action类的主要依赖，提供业务逻辑服务
     */
    @Mock
    protected ServiceFacade serviceFacade;

    /**
     * 基础设施服务门面Mock对象
     * 提供基础设施相关的服务支持
     */
    @Mock
    protected InfraServiceFacade infraServiceFacade;

    /**
     * Mock的对象描述
     * 用于测试中的元数据相关功能
     */
    @Mock
    protected IObjectDescribe mockObjectDescribe;

    /**
     * 测试用的对象数据
     * 标准的业务数据对象
     */
    protected IObjectData testObjectData;

    /**
     * Action测试专用的初始化方法
     * 在BaseServiceTest的基础上添加Action特有的初始化
     */
    @BeforeEach
    void setUpActionBase() {
        // 调用父类的初始化方法
        super.setUpBase();
        
        // 初始化Action专用的测试数据
        initializeActionTestData();
        
        // 配置Mock对象的默认行为
        configureMockDefaults();
    }

    /**
     * 初始化Action测试专用的数据
     */
    private void initializeActionTestData() {
        // 创建标准的测试对象数据，遵循框架标准示例
        testObjectData = new ObjectData();
        testObjectData.put("_id", "test_id_001");
        testObjectData.put("name", "测试对象");
        testObjectData.put("owner", USER_ID);
        testObjectData.put("object_describe_api_name", "TestObj__c");
    }

    /**
     * 配置Mock对象的默认行为
     * 提供常用Mock对象的标准配置，减少重复代码
     */
    private void configureMockDefaults() {
        // 配置mockObjectDescribe的默认行为
        if (mockObjectDescribe != null) {
            // 这些配置在具体测试中可以被重写
            // when(mockObjectDescribe.getApiName()).thenReturn("TestObj__c");
            // when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
            // when(mockObjectDescribe.getTenantId()).thenReturn(TENANT_ID);
            // when(mockObjectDescribe.isActive()).thenReturn(true);
        }
    }

    /**
     * 获取服务名称的默认实现
     * Action测试的标准服务名称
     */
    @Override
    protected String getServiceName() {
        return "ActionTestService";
    }

    /**
     * 创建带有指定数据的IObjectData对象
     * 
     * @param dataJson JSON格式的数据字符串
     * @return IObjectData对象
     */
    protected IObjectData createObjectData(String dataJson) {
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(dataJson);
        return objectData;
    }

    /**
     * 创建标准的测试用户
     * 遵循框架标准示例：User.systemUser('74255')
     * 
     * @return 系统用户对象
     */
    protected User createSystemUser() {
        return User.systemUser(TENANT_ID);
    }

    /**
     * 创建标准的RequestContext
     * 遵循框架标准示例的构造方式
     * 
     * @param user 用户对象
     * @return RequestContext对象
     */
    protected RequestContext createStandardRequestContext(User user) {
        return RequestContext.builder()
                .tenantId(user.getTenantId())
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
    }

    /**
     * 创建标准的ServiceContext
     * 遵循框架标准示例的构造方式
     * 
     * @param requestContext 请求上下文
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return ServiceContext对象
     */
    protected ServiceContext createStandardServiceContext(RequestContext requestContext, 
                                                         String serviceName, 
                                                         String methodName) {
        return new ServiceContext(requestContext, serviceName, methodName);
    }

    /**
     * 为Action对象注入Mock依赖
     * 使用反射方式注入，支持各种Action类型
     * 
     * @param action 待注入的Action对象
     */
    protected void injectMockDependencies(Object action) {
        ActionTestUtils.injectServiceFacade(action, serviceFacade);
        ActionTestUtils.injectInfraServiceFacade(action, infraServiceFacade);
    }

    /**
     * 验证Action执行结果的通用方法
     * 提供标准的验证模式
     * 
     * @param result 执行结果
     * @param expectedType 期望的结果类型
     * @param <T> 结果类型
     * @return 类型转换后的结果
     */
    protected <T> T verifyActionResult(Object result, Class<T> expectedType) {
        if (result == null) {
            throw new AssertionError("Action执行结果不能为null");
        }
        
        if (!expectedType.isInstance(result)) {
            throw new AssertionError(String.format(
                "Action执行结果类型不匹配，期望: %s, 实际: %s", 
                expectedType.getSimpleName(), 
                result.getClass().getSimpleName()
            ));
        }
        
        return expectedType.cast(result);
    }

    /**
     * 创建测试用的JSON数据字符串
     * 提供标准的测试数据格式
     * 
     * @param id 对象ID
     * @param name 对象名称
     * @param owner 所有者
     * @return JSON格式的数据字符串
     */
    protected String createTestDataJson(String id, String name, String owner) {
        return String.format(
            "{\"_id\":\"%s\",\"name\":\"%s\",\"owner\":\"%s\",\"object_describe_api_name\":\"TestObj__c\"}", 
            id, name, owner
        );
    }
}
