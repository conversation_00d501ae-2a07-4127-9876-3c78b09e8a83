package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * GenerateByAI
 * Action测试专用的Mock对象工厂类
 * 
 * 功能特性：
 * - 提供Action测试所需的标准Mock对象
 * - 遵循框架标准示例的对象构造规范
 * - 支持复杂业务场景的Mock配置
 * - 统一的Mock对象行为配置
 * 
 * 设计原则：
 * - 工厂方法模式，统一创建Mock对象
 * - 遵循框架标准示例中的构造方式
 * - 提供灵活的配置选项
 * - 支持链式调用和Builder模式
 */
public class ActionMockFactory {

    /**
     * 默认测试常量
     */
    public static final String DEFAULT_TENANT_ID = "74255";
    public static final String DEFAULT_USER_ID = "1000";
    public static final String DEFAULT_OBJECT_API_NAME = "TestObj__c";
    public static final String DEFAULT_OBJECT_DISPLAY_NAME = "测试对象";

    /**
     * 创建标准的Mock ServiceFacade对象
     * 配置常用方法的默认行为
     * 
     * @return Mock的ServiceFacade对象
     */
    public static ServiceFacade createMockServiceFacade() {
        ServiceFacade mockServiceFacade = Mockito.mock(ServiceFacade.class);
        
        // 配置默认行为，具体测试中可以重写
        // when(mockServiceFacade.findObject(anyString(), anyString())).thenReturn(createMockObjectDescribe());
        
        return mockServiceFacade;
    }

    /**
     * 创建标准的Mock InfraServiceFacade对象
     * 配置常用方法的默认行为
     * 
     * @return Mock的InfraServiceFacade对象
     */
    public static InfraServiceFacade createMockInfraServiceFacade() {
        InfraServiceFacade mockInfraServiceFacade = Mockito.mock(InfraServiceFacade.class);
        
        // 配置默认行为，具体测试中可以重写
        // when(mockInfraServiceFacade.findBySwitchCache(...)).thenReturn(...);
        
        return mockInfraServiceFacade;
    }

    /**
     * 创建标准的Mock IObjectDescribe对象
     * 遵循框架标准示例的构造方式
     * 
     * @return Mock的IObjectDescribe对象
     */
    public static IObjectDescribe createMockObjectDescribe() {
        return createMockObjectDescribe(DEFAULT_OBJECT_API_NAME, DEFAULT_OBJECT_DISPLAY_NAME);
    }

    /**
     * 创建指定API名称的Mock IObjectDescribe对象
     * 
     * @param apiName 对象API名称
     * @param displayName 对象显示名称
     * @return Mock的IObjectDescribe对象
     */
    public static IObjectDescribe createMockObjectDescribe(String apiName, String displayName) {
        IObjectDescribe mockDescribe = Mockito.mock(IObjectDescribe.class);
        
        // 配置基本属性
        when(mockDescribe.getApiName()).thenReturn(apiName);
        when(mockDescribe.getDisplayName()).thenReturn(displayName);
        when(mockDescribe.getTenantId()).thenReturn(DEFAULT_TENANT_ID);
        when(mockDescribe.isActive()).thenReturn(true);
        when(mockDescribe.copy()).thenReturn(mockDescribe);
        
        // 创建基本字段列表
        List<IFieldDescribe> fields = createMockFieldDescribes();
        when(mockDescribe.getFieldDescribes()).thenReturn(fields);
        
        return mockDescribe;
    }

    /**
     * 创建真实的IObjectDescribe对象（非Mock）
     * 遵循框架标准示例的构造方式
     * 
     * @param apiName 对象API名称
     * @return 真实的IObjectDescribe对象
     */
    public static IObjectDescribe createRealObjectDescribe(String apiName) {
        ObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(apiName);
        describe.setDisplayName("测试对象");
        describe.setTenantId(DEFAULT_TENANT_ID);
        
        // 创建字段描述，遵循框架标准示例
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(createFieldDescribeMap("name", IFieldType.TEXT));
        describe.setFieldDescribes(Arrays.asList(fieldDescribe));
        
        return describe;
    }

    /**
     * 创建Mock的字段描述列表
     * 
     * @return Mock的IFieldDescribe列表
     */
    public static List<IFieldDescribe> createMockFieldDescribes() {
        IFieldDescribe nameField = createMockFieldDescribe("name", IFieldType.TEXT, "名称");
        IFieldDescribe ownerField = createMockFieldDescribe("owner", IFieldType.EMPLOYEE, "所有者");
        IFieldDescribe statusField = createMockFieldDescribe("status", IFieldType.SELECT_ONE, "状态");
        
        return Arrays.asList(nameField, ownerField, statusField);
    }

    /**
     * 创建Mock的字段描述对象
     * 
     * @param apiName 字段API名称
     * @param fieldType 字段类型
     * @param displayName 字段显示名称
     * @return Mock的IFieldDescribe对象
     */
    public static IFieldDescribe createMockFieldDescribe(String apiName, String fieldType, String displayName) {
        IFieldDescribe mockField = Mockito.mock(IFieldDescribe.class);
        
        when(mockField.getApiName()).thenReturn(apiName);
        when(mockField.getType()).thenReturn(fieldType);
        when(mockField.getDisplayName()).thenReturn(displayName);
        when(mockField.isRequired()).thenReturn(false);
        when(mockField.isReadonly()).thenReturn(false);
        
        return mockField;
    }

    /**
     * 创建真实的字段描述对象（非Mock）
     * 遵循框架标准示例的构造方式
     * 
     * @param apiName 字段API名称
     * @param fieldType 字段类型
     * @return 真实的IFieldDescribe对象
     */
    public static IFieldDescribe createRealFieldDescribe(String apiName, String fieldType) {
        Map<String, Object> fieldMap = createFieldDescribeMap(apiName, fieldType);
        return FieldDescribeFactory.newInstance(fieldMap);
    }

    /**
     * 创建字段描述的Map配置
     * 遵循框架标准示例的构造方式
     * 
     * @param apiName 字段API名称
     * @param fieldType 字段类型
     * @return 字段配置Map
     */
    private static Map<String, Object> createFieldDescribeMap(String apiName, String fieldType) {
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", apiName);
        fieldMap.put("type", fieldType);
        return fieldMap;
    }

    /**
     * 创建标准的测试用户
     * 遵循框架标准示例：User.systemUser('74255')
     * 
     * @return 系统用户对象
     */
    public static User createSystemUser() {
        return User.systemUser(DEFAULT_TENANT_ID);
    }

    /**
     * 创建指定租户的系统用户
     * 
     * @param tenantId 租户ID
     * @return 系统用户对象
     */
    public static User createSystemUser(String tenantId) {
        return User.systemUser(tenantId);
    }

    /**
     * 创建标准的RequestContext
     * 遵循框架标准示例的构造方式
     * 
     * @return RequestContext对象
     */
    public static RequestContext createStandardRequestContext() {
        User user = createSystemUser();
        return RequestContext.builder()
                .tenantId(DEFAULT_TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
    }

    /**
     * 创建带有指定用户的RequestContext
     * 
     * @param user 用户对象
     * @return RequestContext对象
     */
    public static RequestContext createRequestContext(User user) {
        return RequestContext.builder()
                .tenantId(user.getTenantId())
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
    }

    /**
     * 创建标准的测试数据对象
     * 遵循框架标准示例的构造方式
     * 
     * @return IObjectData对象
     */
    public static IObjectData createStandardTestData() {
        return createTestData("test_id_001", "测试对象", DEFAULT_USER_ID);
    }

    /**
     * 创建指定参数的测试数据对象
     * 
     * @param id 对象ID
     * @param name 对象名称
     * @param owner 所有者
     * @return IObjectData对象
     */
    public static IObjectData createTestData(String id, String name, String owner) {
        IObjectData objectData = new ObjectData();
        objectData.put("_id", id);
        objectData.put("name", name);
        objectData.put("owner", owner);
        objectData.put("object_describe_api_name", DEFAULT_OBJECT_API_NAME);
        return objectData;
    }

    /**
     * 从JSON字符串创建测试数据对象
     * 遵循框架标准示例的构造方式
     * 
     * @param jsonString JSON格式的数据字符串
     * @return IObjectData对象
     */
    public static IObjectData createTestDataFromJson(String jsonString) {
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(jsonString);
        return objectData;
    }

    /**
     * 创建完整的Mock场景
     * 包含所有常用的Mock对象
     * 
     * @return MockScenario对象
     */
    public static MockScenario createCompleteScenario() {
        ServiceFacade serviceFacade = createMockServiceFacade();
        InfraServiceFacade infraServiceFacade = createMockInfraServiceFacade();
        IObjectDescribe objectDescribe = createMockObjectDescribe();
        IObjectData testData = createStandardTestData();
        User user = createSystemUser();
        RequestContext requestContext = createRequestContext(user);
        
        return new MockScenario(serviceFacade, infraServiceFacade, objectDescribe, testData, user, requestContext);
    }

    /**
     * Mock场景封装类
     * 包含测试所需的所有Mock对象
     */
    public static class MockScenario {
        public final ServiceFacade serviceFacade;
        public final InfraServiceFacade infraServiceFacade;
        public final IObjectDescribe objectDescribe;
        public final IObjectData testData;
        public final User user;
        public final RequestContext requestContext;

        public MockScenario(ServiceFacade serviceFacade, InfraServiceFacade infraServiceFacade,
                           IObjectDescribe objectDescribe, IObjectData testData,
                           User user, RequestContext requestContext) {
            this.serviceFacade = serviceFacade;
            this.infraServiceFacade = infraServiceFacade;
            this.objectDescribe = objectDescribe;
            this.testData = testData;
            this.user = user;
            this.requestContext = requestContext;
        }
    }
}
